import { getVisibleDashboards } from './custom-dashboard';
import { DashboardPreference } from '@g17eco/types/insight-custom-dashboard';
import { InsightDashboardOption } from '@routes/summary/insights/utils/sidebar';

describe('getVisibleDashboards', () => {
  const createDashboardOption = (value: string, label: string): InsightDashboardOption => ({
    value,
    label,
  });

  const createPreference = (dashboardId: string, isHidden = false): DashboardPreference => ({
    dashboardId,
    isHidden,
  });

  describe('when showAllDashboards is false', () => {
    it('should return only first 8 dashboards when there are more than 8', () => {
      const dashboardOptions = Array.from({ length: 10 }, (_, i) =>
        createDashboardOption(`dashboard-${i}`, `Dashboard ${i}`)
      );
      const preferences: DashboardPreference[] = [];

      const result = getVisibleDashboards({
        dashboardOptions,
        preferences,
        showAllDashboards: false,
      });

      expect(result.dashboardsToShow).toHaveLength(8);
      expect(result.hiddenDashboards).toHaveLength(2);
      expect(result.hasMoreDashboards).toBe(true);
      expect(result.dashboardsToShow.map(d => d.value)).toEqual(
        Array.from({ length: 8 }, (_, i) => `dashboard-${i}`)
      );
      expect(result.hiddenDashboards.map(d => d.value)).toEqual(['dashboard-8', 'dashboard-9']);
    });

    it('should return all dashboards when there are 8 or fewer', () => {
      const dashboardOptions = Array.from({ length: 6 }, (_, i) =>
        createDashboardOption(`dashboard-${i}`, `Dashboard ${i}`)
      );
      const preferences: DashboardPreference[] = [];

      const result = getVisibleDashboards({
        dashboardOptions,
        preferences,
        showAllDashboards: false,
      });

      expect(result.dashboardsToShow).toHaveLength(6);
      expect(result.hiddenDashboards).toHaveLength(0);
      expect(result.hasMoreDashboards).toBe(false);
      expect(result.dashboardsToShow.map(d => d.value)).toEqual(
        Array.from({ length: 6 }, (_, i) => `dashboard-${i}`)
      );
    });

    it('should return empty arrays when no dashboards provided', () => {
      const result = getVisibleDashboards({
        dashboardOptions: [],
        preferences: [],
        showAllDashboards: false,
      });

      expect(result.dashboardsToShow).toEqual([]);
      expect(result.hiddenDashboards).toEqual([]);
      expect(result.hasMoreDashboards).toBe(false);
    });
  });

  describe('when showAllDashboards is true', () => {
    it('should return all dashboards regardless of count', () => {
      const dashboardOptions = Array.from({ length: 15 }, (_, i) =>
        createDashboardOption(`dashboard-${i}`, `Dashboard ${i}`)
      );
      const preferences: DashboardPreference[] = [];

      const result = getVisibleDashboards({
        dashboardOptions,
        preferences,
        showAllDashboards: true,
      });

      expect(result.dashboardsToShow).toHaveLength(15);
      expect(result.hiddenDashboards).toHaveLength(7); // Still calculates hidden from slice
      expect(result.hasMoreDashboards).toBe(true);
      expect(result.dashboardsToShow.map(d => d.value)).toEqual(
        Array.from({ length: 15 }, (_, i) => `dashboard-${i}`)
      );
    });

    it('should return all dashboards when there are 8 or fewer', () => {
      const dashboardOptions = Array.from({ length: 5 }, (_, i) =>
        createDashboardOption(`dashboard-${i}`, `Dashboard ${i}`)
      );
      const preferences: DashboardPreference[] = [];

      const result = getVisibleDashboards({
        dashboardOptions,
        preferences,
        showAllDashboards: true,
      });

      expect(result.dashboardsToShow).toHaveLength(5);
      expect(result.hiddenDashboards).toHaveLength(0);
      expect(result.hasMoreDashboards).toBe(false);
    });
  });

  describe('preferences filtering', () => {
    it('should filter out hidden dashboards', () => {
      const dashboardOptions = [
        createDashboardOption('dashboard-1', 'Dashboard 1'),
        createDashboardOption('dashboard-2', 'Dashboard 2'),
        createDashboardOption('dashboard-3', 'Dashboard 3'),
      ];
      const preferences = [
        createPreference('dashboard-2', true), // hidden
      ];

      const result = getVisibleDashboards({
        dashboardOptions,
        preferences,
        showAllDashboards: true,
      });

      expect(result.dashboardsToShow).toHaveLength(2);
      expect(result.dashboardsToShow.map(d => d.value)).toEqual(['dashboard-1', 'dashboard-3']);
    });

    it('should show dashboards when isHidden is false or undefined', () => {
      const dashboardOptions = [
        createDashboardOption('dashboard-1', 'Dashboard 1'),
        createDashboardOption('dashboard-2', 'Dashboard 2'),
        createDashboardOption('dashboard-3', 'Dashboard 3'),
      ];
      const preferences = [
        createPreference('dashboard-1', false),
        createPreference('dashboard-2'),
      ];

      const result = getVisibleDashboards({
        dashboardOptions,
        preferences,
        showAllDashboards: true,
      });

      expect(result.dashboardsToShow).toHaveLength(3);
      expect(result.dashboardsToShow.map(d => d.value)).toEqual(['dashboard-1', 'dashboard-2', 'dashboard-3']);
    });
  });

  describe('ordering', () => {
    it('should order dashboards based on preference order', () => {
      const dashboardOptions = [
        createDashboardOption('dashboard-1', 'Dashboard 1'),
        createDashboardOption('dashboard-2', 'Dashboard 2'),
        createDashboardOption('dashboard-3', 'Dashboard 3'),
        createDashboardOption('dashboard-4', 'Dashboard 4'),
      ];
      const preferences = [
        createPreference('dashboard-3'), // index 0
        createPreference('dashboard-1'), // index 1
        createPreference('dashboard-4'), // index 2
      ];

      const result = getVisibleDashboards({
        dashboardOptions,
        preferences,
        showAllDashboards: true,
      });

      expect(result.dashboardsToShow.map(d => d.value)).toEqual(['dashboard-3', 'dashboard-1', 'dashboard-4', 'dashboard-2']);
    });

    it('should put dashboards without preferences after ordered ones', () => {
      const dashboardOptions = [
        createDashboardOption('dashboard-1', 'Dashboard 1'),
        createDashboardOption('dashboard-2', 'Dashboard 2'), // no preference
        createDashboardOption('dashboard-3', 'Dashboard 3'),
        createDashboardOption('dashboard-4', 'Dashboard 4'), // no preference
      ];
      const preferences = [
        createPreference('dashboard-3'), // index 0
        createPreference('dashboard-1'), // index 1
      ];

      const result = getVisibleDashboards({
        dashboardOptions,
        preferences,
        showAllDashboards: true,
      });

      expect(result.dashboardsToShow.map(d => d.value)).toEqual(['dashboard-3', 'dashboard-1', 'dashboard-2', 'dashboard-4']);
    });

    it('should maintain relative order of dashboards without preferences', () => {
      const dashboardOptions = [
        createDashboardOption('dashboard-1', 'Dashboard 1'), // no preference
        createDashboardOption('dashboard-2', 'Dashboard 2'), // no preference
        createDashboardOption('dashboard-3', 'Dashboard 3'), // no preference
      ];
      const preferences: DashboardPreference[] = [];

      const result = getVisibleDashboards({
        dashboardOptions,
        preferences,
        showAllDashboards: true,
      });

      expect(result.dashboardsToShow.map(d => d.value)).toEqual(['dashboard-1', 'dashboard-2', 'dashboard-3']);
    });
  });

  describe('complex scenarios', () => {
    it('should handle combination of hidden dashboards, ordering, and limit', () => {
      const dashboardOptions = Array.from({ length: 12 }, (_, i) =>
        createDashboardOption(`dashboard-${i}`, `Dashboard ${i}`)
      );
      const preferences = [
        createPreference('dashboard-2', true), // hidden
        createPreference('dashboard-5'), // index 0
        createPreference('dashboard-1'), // index 1
        createPreference('dashboard-8', true), // hidden
        createPreference('dashboard-3'), // index 2
      ];

      const result = getVisibleDashboards({
        dashboardOptions,
        preferences,
        showAllDashboards: false,
      });

      const expectedVisible = ['dashboard-5', 'dashboard-1', 'dashboard-3', 'dashboard-0', 'dashboard-4', 'dashboard-6', 'dashboard-7', 'dashboard-9'];
      const expectedHidden = ['dashboard-10', 'dashboard-11'];

      expect(result.dashboardsToShow.map(d => d.value)).toEqual(expectedVisible);
      expect(result.hiddenDashboards.map(d => d.value)).toEqual(expectedHidden);
      expect(result.hasMoreDashboards).toBe(true);
    });

    it('should handle empty preferences array', () => {
      const dashboardOptions = [
        createDashboardOption('dashboard-1', 'Dashboard 1'),
        createDashboardOption('dashboard-2', 'Dashboard 2'),
      ];
      const preferences: DashboardPreference[] = [];

      const result = getVisibleDashboards({
        dashboardOptions,
        preferences,
        showAllDashboards: true,
      });

      expect(result.dashboardsToShow.map(d => d.value)).toEqual(['dashboard-1', 'dashboard-2']);
      expect(result.hasMoreDashboards).toBe(false);
    });
  });
});