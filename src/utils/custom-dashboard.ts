import { DashboardPreference } from '@g17eco/types/insight-custom-dashboard';
import { InsightDashboardOption } from '@routes/summary/insights/utils/sidebar';

export const sortDashboards = (a: { orderIndex?: number }, b: { orderIndex?: number }) => {
  // tems with orderIndex go first, sorted by that index;
  // items without orderIndex go after, keeping their relative order
  if (a.orderIndex !== undefined && b.orderIndex !== undefined) {
    return a.orderIndex - b.orderIndex;
  }
  if (a.orderIndex !== undefined) return -1;
  if (b.orderIndex !== undefined) return 1;
  return 0;
};

// filter hidden, apply order
const applyPreferences = (dashboardOptions: InsightDashboardOption[], preferences: DashboardPreference[]) => {
  const preferenceOrderMap = new Map<string, number>();
  preferences.forEach((pref, index) => {
    preferenceOrderMap.set(pref.dashboardId, index);
  });

  return dashboardOptions
    .map((dashboardOption) => {
      const preference = preferences.find((p) => p.dashboardId === dashboardOption.value);
      const orderIndex = preferenceOrderMap.get(dashboardOption.value);
      return {
        dashboardOption,
        orderIndex,
        isHidden: Boolean(preference?.isHidden),
      };
    })
    .filter((item) => !item.isHidden)
    .sort(sortDashboards)
    .map((item) => item.dashboardOption);
};

const HIDDEN_DASHBOARD_LIMIT = 8;

export const getVisibleDashboards = ({
  dashboardOptions,
  preferences,
  showAllDashboards,
}: {
  dashboardOptions: InsightDashboardOption[];
  preferences: DashboardPreference[];
  showAllDashboards: boolean;
}) => {
  const visibleDashboards = applyPreferences(dashboardOptions, preferences);
  const dashboardsToShow = showAllDashboards ? visibleDashboards : visibleDashboards.slice(0, HIDDEN_DASHBOARD_LIMIT);
  const hiddenDashboards = visibleDashboards.slice(HIDDEN_DASHBOARD_LIMIT);
  const hasMoreDashboards = visibleDashboards.length > HIDDEN_DASHBOARD_LIMIT;

  return {
    dashboardsToShow,
    hiddenDashboards,
    hasMoreDashboards,
  };
};
