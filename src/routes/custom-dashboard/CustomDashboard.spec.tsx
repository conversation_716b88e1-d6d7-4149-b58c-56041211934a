import { describe, it, expect, vi, beforeEach, afterEach, beforeAll, afterAll } from 'vitest';
import { renderWithProviders, screen } from '@fixtures/utils';
import userEvent from '@testing-library/user-event';
import { CustomDashboard } from './CustomDashboard';
import { createInsightDashboard } from '@fixtures/custom-dashboard-factory';
import { createChartItem, createTextItem } from '@fixtures/dashboard-items';
import { initiativeOne } from '@fixtures/initiative-factory';
import { DataPeriods } from '@utils/dataPeriods';
import { createSurveyData } from '@fixtures/survey-factory';
import { CustomDashboardContext, InsightDashboardContextProps } from './context/CustomDashboardWrapper';
import { FeaturePermissions } from '@services/permissions/FeaturePermissions';
import { PERMISSION_GROUPS } from '@utils/permission-groups';
import { faker } from '@faker-js/faker';
import { simpleUserStore } from '@fixtures/redux-store';
import { SafeInitiativeFields } from '@g17eco/types/initiative';
import { setupServer } from 'msw/node';
import { http } from 'msw';
import { getUrl, ResponseSuccess } from '@fixtures/msw-fixtures';

// Mock html2canvas to avoid CSS parsing errors
vi.mock('html2canvas', () => ({
  default: vi.fn(() =>
    Promise.resolve({
      toDataURL: () =>
        'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==',
    }),
  ),
}));

vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useLocation: () => ({ search: '' }),
  };
});

// Mock jsPDF to avoid PNG processing errors
vi.mock('jspdf', () => {
  class MockJsPDF {
    addImage = vi.fn().mockReturnThis();
    addPage = vi.fn().mockReturnThis();
    save = vi.fn();
    internal = {
      pageSize: {
        getWidth: () => 210,
        getHeight: () => 297,
      },
    };
  }

  return {
    default: MockJsPDF,
  };
});

describe('CustomDashboard', () => {
  // MSW Server Setup
  const server = setupServer();

  beforeAll(() => server.listen());

  afterAll(() => server.close());

  const mockHandleClickEdit = vi.fn();
  const mockHandleSave = vi.fn();
  const mockHandleDelete = vi.fn();
  const mockHandleOpenUtrvHistoryModal = vi.fn();
  const mockSetPeriod = vi.fn();
  const mockOnChangeDateRange = vi.fn();
  const mockHandleClickOption = vi.fn();
  const mockSetEditingItem = vi.fn();

  const dashboard = createInsightDashboard({
    title: 'Test Dashboard',
    items: [
      createChartItem({ _id: 'chart-1', title: 'Test Chart' }),
      createTextItem({ _id: 'text-1', text: 'Test Text' }),
    ],
  });

  const mockInitiative: SafeInitiativeFields = {
    _id: initiativeOne._id,
    name: initiativeOne.name,
    profile: '',
    sectorText: 'Technology',
    industryText: 'Software',
    description: 'Test initiative description',
    missionStatement: 'Test mission statement',
  };

  const defaultProps = {
    dashboard,
    handleClickEdit: mockHandleClickEdit,
    handleSave: mockHandleSave,
    handleDelete: mockHandleDelete,
    handleOpenUtrvHistoryModal: mockHandleOpenUtrvHistoryModal,
    canManage: true,
    initiative: mockInitiative,
    isCurrentLevelDashboard: true,
    survey: createSurveyData(),
    availablePeriods: [DataPeriods.Yearly, DataPeriods.Quarterly],
    period: DataPeriods.Yearly,
    setPeriod: mockSetPeriod,
    dateRange: undefined,
    timeRange: undefined,
    onChangeDateRange: mockOnChangeDateRange,
    currentPage: 'custom' as const,
    dashboardOptions: [],
    handleClickOption: mockHandleClickOption,
    setEditingItem: mockSetEditingItem,
    rootOrg: undefined,
    toggleManageModal: vi.fn(),
  };

  const mockContextValue: InsightDashboardContextProps = {
    getHistoricalUtrsByCodes: vi.fn().mockResolvedValue([]),
    hideOptions: [],
    hideShareButton: false,
    hideQuestionReference: false,
    readOnly: false,
    components: {},
    duplicateDashboard: vi.fn().mockResolvedValue(undefined),
    integrationProviders: undefined,
    userIsStaff: false,
  };

  const renderWithContext = (ui: React.ReactElement) => {
    return renderWithProviders(
      <CustomDashboardContext.Provider value={mockContextValue}>{ui}</CustomDashboardContext.Provider>,
      { store: simpleUserStore },
    );
  };

  beforeEach(() => {
    vi.spyOn(FeaturePermissions, 'canAccessCustomDashboards').mockReturnValue(true);
    server.use(
      // Blueprint questions endpoint
      http.get(getUrl('initiatives/:initiativeId/blueprint/blueprint2022/questions'), async () => ResponseSuccess([])),
      // Organization used scopes endpoint
      http.get(getUrl('/organizations/:organizationId/scopes/used'), async () =>
        ResponseSuccess({ standards: {}, frameworks: {} }),
      ),
      // Organization metric groups endpoint
      http.get(getUrl('/organizations/:organizationId/metric-groups'), async () => ResponseSuccess([])),
    );
  });

  afterEach(() => {
    server.resetHandlers();
    vi.clearAllMocks();
    vi.restoreAllMocks();
  });

  describe('rendering', () => {
    it('renders dashboard properly', async () => {
      renderWithContext(<CustomDashboard {...defaultProps} />);

      expect(screen.getByTestId('grid-container')).toBeInTheDocument();

      expect((await screen.findAllByText('Test Chart'))[0]).toBeInTheDocument();
      expect(screen.getByText('Test Text')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /edit/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /export dashboard/i })).toBeInTheDocument();
    });

    it('does not render edit button when user cannot manage', async () => {
      renderWithContext(<CustomDashboard {...defaultProps} canManage={false} />);

      // Wait for any charts to render before checking
      await screen.findByTestId('grid-container');
      expect(screen.queryByRole('button', { name: /edit/i })).not.toBeInTheDocument();
    });

    it('does not render edit button when not current level dashboard', async () => {
      renderWithContext(<CustomDashboard {...defaultProps} isCurrentLevelDashboard={false} />);

      // Wait for any charts to render before checking
      await screen.findByTestId('grid-container');
      expect(screen.queryByRole('button', { name: /edit/i })).not.toBeInTheDocument();
    });
  });

  describe('user interactions', () => {
    it('calls handleClickEdit when edit button is clicked', async () => {
      const user = userEvent.setup();
      renderWithContext(<CustomDashboard {...defaultProps} />);

      const editButton = screen.getByRole('button', { name: /edit/i });
      await user.click(editButton);

      expect(mockHandleClickEdit).toHaveBeenCalledTimes(1);
    });

    it('calls exportToPDF when export button is clicked', async () => {
      const user = userEvent.setup();
      renderWithContext(<CustomDashboard {...defaultProps} />);

      const exportButton = screen.getByRole('button', { name: /export dashboard/i });
      await user.click(exportButton);

      expect(exportButton).toBeInTheDocument();
    });
  });

  describe('company profile', () => {
    it('renders company profile when initiativeInfo filter is enabled', async () => {
      const dashboardWithInfo = createInsightDashboard({
        ...dashboard,
        filters: {
          ...dashboard.filters,
          initiativeInfo: { enabled: true },
        },
      });

      renderWithContext(<CustomDashboard {...defaultProps} dashboard={dashboardWithInfo} />);

      // Wait for any charts to render before checking
      await screen.findByTestId('grid-container');
      expect(screen.getByText(mockInitiative.name)).toBeInTheDocument();
    });

    it('does not render company profile when initiativeInfo filter is disabled', async () => {
      renderWithContext(<CustomDashboard {...defaultProps} />);

      // Wait for any charts to render before checking
      await screen.findByTestId('grid-container');
      expect(screen.queryByText(mockInitiative.name)).not.toBeInTheDocument();
    });
  });

  it('renders static SDG chart when filter is enabled and no SDG item exists', async () => {
    const dashboardWithSDG = createInsightDashboard({
      ...dashboard,
      filters: {
        ...dashboard.filters,
        sdgContribution: { enabled: true },
      },
      scorecard: {
        scorecard: { goals: [] },
        initiative: { _id: faker.database.mongodbObjectId(), permissionGroup: PERMISSION_GROUPS.COMPANY_TRACKER_PRO },
      },
    });

    renderWithContext(<CustomDashboard {...defaultProps} dashboard={dashboardWithSDG} />);

    expect(await screen.findByTestId('sdg-chart')).toBeInTheDocument();
  });

  it('handles empty dashboard items', () => {
    const emptyDashboard = createInsightDashboard({ items: [] });

    renderWithContext(<CustomDashboard {...defaultProps} dashboard={emptyDashboard} />);

    const gridContainer = screen.getByTestId('grid-container');
    expect(gridContainer.querySelectorAll('.react-grid-item')).toHaveLength(0);
  });

  it('renders export button', async () => {
    renderWithContext(<CustomDashboard {...defaultProps} />);

    // Wait for any charts to render before checking
    await screen.findByTestId('grid-container');
    const exportButton = screen.getByRole('button', { name: /export dashboard/i });
    expect(exportButton).toBeInTheDocument();
    expect(exportButton).not.toBeDisabled();
  });
});
