import { screen, waitFor } from '@testing-library/react';
import { vi } from 'vitest';
import { ManageDashboardsModal } from './ManageDashboardsModal';
import { setupSimple } from '@fixtures/utils';
import type { TemplateOption } from '@g17eco/types/insight-custom-dashboard';
import { DashboardTemplateType } from '@g17eco/types/insight-custom-dashboard';
import { InsightDashboardOption } from '@routes/summary/insights/utils/sidebar';

const mockDashboardOption: InsightDashboardOption = {
  value: 'dashboard-1',
  label: 'Test Dashboard',
};
const sharedDashboardOption: InsightDashboardOption = {
  value: 'shared-dashboard-1',
  label: 'Shared Dashboard',
  isSharedByParent: true,
};
const mockTemplateOptions: TemplateOption[] = [{ value: DashboardTemplateType.WFN, label: 'WFN Template' }];

const defaultProps = {
  isOpen: true,
  toggle: vi.fn(),
  initiativeId: 'initiative-1',
  dashboardOptions: [mockDashboardOption],
  initialPreferences: [],
  templateOptions: mockTemplateOptions,
  isUpdatingPreferences: false,
  handleAddNew: vi.fn(),
  handleSavePreferences: vi.fn(),
  handleDuplicateDashboard: vi.fn(),
  handleDeleteDashboard: vi.fn(),
  handleCreateTemplate: vi.fn(),
};

describe('ManageDashboardsModal', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders modal with dashboard list', () => {
    setupSimple(<ManageDashboardsModal {...defaultProps} />);

    expect(screen.getByText('Manage Dashboards')).toBeInTheDocument();
    expect(screen.getByText('Test Dashboard')).toBeInTheDocument();
    expect(screen.getByText('Add dashboard')).toBeInTheDocument();
  });

  it('toggles dashboard visibility', async () => {
    const { user } = setupSimple(<ManageDashboardsModal {...defaultProps} />);

    const visibilityButton = document.querySelector('.fa-eye');
    expect(visibilityButton).toBeInTheDocument();
    await user.click(visibilityButton!);

    expect(document.querySelector('.fa-eye-slash')).toBeInTheDocument();
  });

  it('calls onDuplicate when duplicate button is clicked', async () => {
    const { user } = setupSimple(<ManageDashboardsModal {...defaultProps} />);

    const duplicateButton = document.querySelector('.fa-copy');
    expect(duplicateButton).toBeInTheDocument();
    await user.click(duplicateButton!);

    expect(defaultProps.handleDuplicateDashboard).toHaveBeenCalledWith(mockDashboardOption.value);
  });

  it('shows delete confirmation when delete button is clicked', async () => {
    const { user } = setupSimple(<ManageDashboardsModal {...defaultProps} />);

    const deleteButton = document.querySelector('.fa-trash');
    expect(deleteButton).toBeInTheDocument();
    await user.click(deleteButton!);

    expect(screen.getByText('Delete dashboard')).toBeInTheDocument();
    expect(screen.getByText(/Are you sure you want to delete "Test Dashboard"?/)).toBeInTheDocument();
  });

  it('calls onDelete when delete is confirmed', async () => {
    const { user } = setupSimple(<ManageDashboardsModal {...defaultProps} />);

    const deleteButton = document.querySelector('.fa-trash');
    expect(deleteButton).toBeInTheDocument();
    await user.click(deleteButton!);

    const deleteInput = screen.getByPlaceholderText('DELETE');
    await user.type(deleteInput, 'DELETE');

    const confirmButton = screen.getByRole('button', { name: 'Delete' });
    await user.click(confirmButton);

    await waitFor(() => {
      expect(defaultProps.handleDeleteDashboard).toHaveBeenCalledWith(mockDashboardOption.value);
    });
  });

  it('calls onSave when save button is clicked', async () => {
    const { user } = setupSimple(<ManageDashboardsModal {...defaultProps} />);

    const saveButton = screen.getByRole('button', { name: /save/i });
    await user.click(saveButton);

    await waitFor(() => {
      expect(defaultProps.handleSavePreferences).toHaveBeenCalledWith([]);
    });
  });

  it('calls toggle when cancel button is clicked', async () => {
    const { user } = setupSimple(<ManageDashboardsModal {...defaultProps} />);

    const cancelButton = screen.getByRole('button', { name: /cancel/i });
    await user.click(cancelButton);

    expect(defaultProps.toggle).toHaveBeenCalled();
  });

  it('shows template menu when add dashboard is clicked with templates', async () => {
    const { user } = setupSimple(<ManageDashboardsModal {...defaultProps} />);

    const addButton = screen.getByRole('button', { name: /add dashboard/i });
    await user.click(addButton);

    expect(screen.getByText('Blank template')).toBeInTheDocument();
    expect(screen.getByText('WFN Template')).toBeInTheDocument();
  });

  it('calls onAddNew when blank template is selected and title is entered', async () => {
    const { user } = setupSimple(<ManageDashboardsModal {...defaultProps} />);

    const addButton = screen.getByRole('button', { name: /add dashboard/i });
    await user.click(addButton);

    const blankOption = screen.getByText('Blank template');
    await user.click(blankOption);

    const titleInput = screen.getByPlaceholderText('Enter dashboard title');
    await user.type(titleInput, 'Test Dashboard{enter}');

    expect(defaultProps.handleAddNew).toHaveBeenCalledWith({
      title: 'Custom DashboardTest Dashboard',
      createInstantly: true,
    });
    expect(defaultProps.toggle).toHaveBeenCalled();
  });

  it('calls onCreateTemplate when WFN template is selected and save is clicked', async () => {
    const { user } = setupSimple(<ManageDashboardsModal {...defaultProps} />);

    const addButton = screen.getByRole('button', { name: /add dashboard/i });
    await user.click(addButton);

    const wfnOption = screen.getByText('WFN Template');
    await user.click(wfnOption);

    const saveButton = screen.getByRole('button', { name: 'Save' });
    await user.click(saveButton);

    expect(defaultProps.handleCreateTemplate).toHaveBeenCalledWith(DashboardTemplateType.WFN);
    expect(defaultProps.toggle).toHaveBeenCalled();
  });

  it('shows title input when no templates available and add dashboard is clicked', async () => {
    const propsWithoutTemplates = { ...defaultProps, templateOptions: [] };
    const { user } = setupSimple(<ManageDashboardsModal {...propsWithoutTemplates} />);

    const addButton = screen.getByRole('button', { name: /add dashboard/i });
    await user.click(addButton);

    const titleInput = screen.getByPlaceholderText('Enter dashboard title');
    expect(titleInput).toBeInTheDocument();
  });

  it('disables save button when updating preferences', () => {
    const propsUpdating = { ...defaultProps, isUpdatingPreferences: true };
    setupSimple(<ManageDashboardsModal {...propsUpdating} />);

    const saveButton = screen.getByRole('button', { name: /saving/i });
    expect(saveButton).toBeDisabled();
  });

  it('renders drag handle for reordering', () => {
    setupSimple(<ManageDashboardsModal {...defaultProps} />);

    const dragHandle = document.querySelector('.fa-grip-dots-vertical');
    expect(dragHandle).toBeInTheDocument();
  });
  
  it('disables duplicate and delete buttons when dashboard is shared by parent', () => {
    const propsWithSharedDashboard = {
      ...defaultProps,
      dashboardOptions: [sharedDashboardOption],
    };

    setupSimple(<ManageDashboardsModal {...propsWithSharedDashboard} />);

    const duplicateButton = document.querySelector('.fa-copy')?.closest('button');
    const deleteButton = document.querySelector('.fa-trash')?.closest('button');

    expect(duplicateButton).toBeDisabled();
    expect(deleteButton).toBeDisabled();
  });

  it('enables duplicate and delete buttons when dashboard is not shared by parent', () => {
    const regularDashboardOption: InsightDashboardOption = {
      value: 'regular-dashboard-1',
      label: 'Regular Dashboard',
      isSharedByParent: false,
    };

    const propsWithRegularDashboard = {
      ...defaultProps,
      dashboardOptions: [regularDashboardOption],
    };

    setupSimple(<ManageDashboardsModal {...propsWithRegularDashboard} />);

    const duplicateButton = document.querySelector('.fa-copy')?.closest('button');
    const deleteButton = document.querySelector('.fa-trash')?.closest('button');

    expect(duplicateButton).not.toBeDisabled();
    expect(deleteButton).not.toBeDisabled();
  });
});
