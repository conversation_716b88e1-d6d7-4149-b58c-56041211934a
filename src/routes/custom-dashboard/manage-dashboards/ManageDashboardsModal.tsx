import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, Input, <PERSON>lap<PERSON>, Row, Col } from 'reactstrap';
import {
  DashboardPreference,
  DashboardTemplateType,
  OutputHookCustomDashboards,
  TemplateOption,
} from '@g17eco/types/insight-custom-dashboard';
import { DeleteConfirmation } from '@g17eco/molecules/confirm-modal/DeleteConfirmation';
import { TableDraggableRows } from '@g17eco/molecules/table-draggable-rows';
import IconButton from '@g17eco/molecules/button/IconButton';
import { sortDashboards } from '@utils/custom-dashboard';
import { InsightDashboardOption } from '@routes/summary/insights/utils/sidebar';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';
import { TemplateMenuPopover } from '@features/custom-dashboard';
import { useToggle } from '@hooks/useToggle';

const TEMPLATE_MENU_TARGET = 'manage-dashboard-template-menu';

interface ManageDashboardsModalProps
  extends Pick<
    OutputHookCustomDashboards,
    | 'dashboardOptions'
    | 'isUpdatingPreferences'
    | 'handleAddNew'
    | 'handleSavePreferences'
    | 'handleDuplicateDashboard'
    | 'handleDeleteDashboard'
  > {
  isOpen: boolean;
  toggle: () => void;
  initiativeId: string;
  initialPreferences: DashboardPreference[];
  templateOptions: TemplateOption[];
  handleCreateTemplate?: (templateType: DashboardTemplateType) => void;
}

export const ManageDashboardsModal = (props: ManageDashboardsModalProps) => {
  const {
    isOpen,
    toggle,
    dashboardOptions,
    initialPreferences,
    templateOptions,
    isUpdatingPreferences,
    handleSavePreferences,
    handleDuplicateDashboard,
    handleDeleteDashboard,
    handleAddNew,
    handleCreateTemplate,
  } = props;

  const [preferences, setPreferences] = useState<DashboardPreference[]>(initialPreferences);
  const [deleteTarget, setDeleteTarget] = useState<InsightDashboardOption | null>(null);
  const [showTemplateMenu, toggleTemplateMenu, setShowTemplateMenu] = useToggle(false);
  const [selectedTemplate, setSelectedTemplate] = useState<DashboardTemplateType | null>(null);
  const [dashboardTitle, setDashboardTitle] = useState('');

  const preferenceOrderMap = new Map<string, number>();
  preferences.forEach((pref, index) => {
    preferenceOrderMap.set(pref.dashboardId, index);
  });

  const mergedPreferenceOptions = dashboardOptions
    .map((dashboard) => {
      const pref = preferences.find((p) => p.dashboardId === dashboard.value);
      const orderIndex = preferenceOrderMap.get(dashboard.value);
      return {
        dashboard,
        orderIndex,
        isHidden: Boolean(pref?.isHidden),
      };
    })
    .sort(sortDashboards);

  const toggleVisibility = (dashboardId: string) => {
    setPreferences((prev) => {
      const existing = prev.find((p) => p.dashboardId === dashboardId);
      if (existing) {
        return prev.map((p) => (p.dashboardId === dashboardId ? { ...p, isHidden: !p.isHidden } : p));
      }

      return [...prev, { dashboardId, isHidden: true }];
    });
  };

  const handleDragStop = (orderedIds: string[]) => {
    const newPreferences = orderedIds.map((dashboardId) => {
      const item = mergedPreferenceOptions.find((d) => d.dashboard.value === dashboardId);
      return {
        dashboardId,
        isHidden: Boolean(item?.isHidden),
      };
    });
    setPreferences(newPreferences);
  };

  const handleSave = async () => {
    await handleSavePreferences(preferences);
    if (selectedTemplate) {
      handleTemplateSelect();
    } else {
      toggle();
    }
  };

  const handleConfirmDelete = async () => {
    if (deleteTarget) {
      await handleDeleteDashboard(deleteTarget.value);
      await handleSavePreferences(preferences);
      setDeleteTarget(null);
    }
  };

  const handleTemplateChange = async (option: DashboardTemplateType | null) => {
    if (!option) {
      return;
    }
    setSelectedTemplate(option);
    if (option === DashboardTemplateType.Blank) {
      setDashboardTitle('Custom Dashboard');
      setShowTemplateMenu(false);
      return;
    } else if (handleCreateTemplate) {
      await handleSavePreferences(preferences);
      handleCreateTemplate(option);
      toggle();
    }
  };

  const handleCreateBlankDashboard = () => {
    const trimmedTitle = dashboardTitle.trim();

    if (!trimmedTitle) {
      return;
    }
    handleAddNew({ title: trimmedTitle, createInstantly: true });
    toggle();
  };

  const handleTitleKeyPress = async (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key !== 'Enter') {
      return;
    }

    const trimmedTitle = dashboardTitle.trim();
    if (!trimmedTitle) {
      return;
    }

    await handleSavePreferences(preferences);
    handleAddNew({ title: trimmedTitle, createInstantly: true });
    toggle();
  };

  const handleTemplateSelect = () => {
    if (!selectedTemplate) {
      return;
    }

    if (selectedTemplate === DashboardTemplateType.Blank) {
      handleCreateBlankDashboard();
      return;
    }

    if (handleCreateTemplate) {
      handleCreateTemplate(selectedTemplate);
      toggle();
    }
  };

  return (
    <>
      <Modal isOpen={isOpen} toggle={toggle} size='lg' className='manage-dashboards-modal'>
        <ModalHeader toggle={toggle}>Manage Dashboards</ModalHeader>
        <ModalBody>
          <TableDraggableRows
            columns={[{ header: 'Dashboard name' }]}
            data={mergedPreferenceOptions.map((item) => ({
              id: item.dashboard.value,
              cols: [
                <div className='d-flex align-items-center justify-content-between w-100'>
                  <div className={item.isHidden ? 'text-ThemeTextPlaceholder' : ''}>{item.dashboard.label}</div>
                  <div className='d-flex align-items-center gap-2'>
                    <SimpleTooltip text={item.isHidden ? 'Dashboard hidden' : 'Dashboard visible'}>
                      <IconButton
                        outline={false}
                        color='transparent'
                        icon={`fa-light ${item.isHidden ? 'fa-eye-slash' : 'fa-eye'}`}
                        className='text-ThemeIconSecondary text-xs'
                        onClick={() => toggleVisibility(item.dashboard.value)}
                      />
                    </SimpleTooltip>
                    <SimpleTooltip
                      text={
                        item.dashboard.isSharedByParent ? 'Dashboard shared by parent, cannot duplicate' : 'Duplicate'
                      }
                    >
                      <IconButton
                        outline={false}
                        color='transparent'
                        icon='fal fa-copy'
                        className='text-ThemeIconSecondary text-xs'
                        onClick={() => handleDuplicateDashboard(item.dashboard.value)}
                        disabled={item.dashboard.isSharedByParent}
                      />
                    </SimpleTooltip>
                    <SimpleTooltip
                      text={item.dashboard.isSharedByParent ? 'Dashboard shared by parent, cannot delete' : 'Delete'}
                    >
                      <IconButton
                        outline={false}
                        color='transparent'
                        icon='fal fa-trash'
                        className='text-ThemeDangerMedium text-xs'
                        onClick={() => setDeleteTarget(item.dashboard)}
                        disabled={item.dashboard.isSharedByParent}
                      />
                    </SimpleTooltip>
                  </div>
                </div>,
              ],
            }))}
            handleUpdate={handleDragStop}
          />
          <div className='d-flex justify-content-end mt-3'>
            <Button
              id={TEMPLATE_MENU_TARGET}
              color='transparent'
              onClick={toggleTemplateMenu}
              className='d-flex align-items-center text-ThemeAccentMedium'
            >
              <i className='fa-light fa-plus mr-2 fs-6 text-ThemeAccentMedium' />
              <span className='mt-1'>Add dashboard</span>
            </Button>
            <TemplateMenuPopover
              target={TEMPLATE_MENU_TARGET}
              open={showTemplateMenu}
              toggle={toggleTemplateMenu}
              onClickOption={handleTemplateChange}
              templates={templateOptions}
              onClickOutside={() => setShowTemplateMenu(false)}
            />
          </div>
          <Collapse className='mt-4 ' isOpen={selectedTemplate === DashboardTemplateType.Blank}>
            <Row className='justify-content-center'>
              <Col xs={12} md={8}>
                <div className='strong'>Title</div>
                <Input
                  type='text'
                  placeholder='Enter dashboard title'
                  value={dashboardTitle}
                  onChange={(e) => setDashboardTitle(e.target.value)}
                  onKeyUp={handleTitleKeyPress}
                  autoFocus
                  className='mt-2'
                />
              </Col>
            </Row>
          </Collapse>
        </ModalBody>
        <ModalFooter>
          <Button color='link-secondary' onClick={toggle}>
            Cancel
          </Button>
          <Button color='primary' onClick={handleSave} disabled={isUpdatingPreferences}>
            {isUpdatingPreferences ? 'Saving...' : 'Save'}
          </Button>
        </ModalFooter>
      </Modal>

      {deleteTarget ? (
        <DeleteConfirmation
          title='Delete dashboard'
          text={`Are you sure you want to delete "${deleteTarget.label}"? This cannot be undone.`}
          handleConfirm={handleConfirmDelete}
          toggle={() => setDeleteTarget(null)}
        />
      ) : null}
    </>
  );
};
