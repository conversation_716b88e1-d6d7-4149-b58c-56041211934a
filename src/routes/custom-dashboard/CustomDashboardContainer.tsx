import { useState } from 'react';
import Dashboard, { DashboardSection, DashboardSectionTitle } from '@g17eco/molecules/dashboard';
import {
  DashboardItemFilters,
  GridDashboardItem,
  HistoricalUtrs,
  InsightDashboard,
  HistoricalUtrsQueryByCodeParams,
} from '../../types/insight-custom-dashboard';
import { CompanyInsightsSidebar } from '../summary/insights/partials/sidebar/CompanyInsightsSidebar';
import { CustomDashboard } from './CustomDashboard';
import { CustomDashboardEditing } from './CustomDashboardEditing';
import { useHistory, useParams } from 'react-router-dom';
import {
  useGetInsightDashboardByIdQuery,
  useGetInsightDashboardIntegrationsQuery,
  useUploadInsightDashboardMediaFilesMutation,
} from '@api/insight-dashboards';
import { generateUrl } from '../util';
import { ROUTES } from '../../constants/routes';
import { Loader } from '@g17eco/atoms/loader';
import { RootState, useAppSelector } from '../../reducers';
import { canManageCurrentLevel, getCurrentUser } from '../../selectors/user';
import { FeaturePermissions } from '../../services/permissions/FeaturePermissions';
import { getRootConfig } from '../../selectors/globalData';
import { getFirstValueListCode, hasUtrvHistoryModal } from './utils';
import { useLazyGetHistoricalUtrsByCodesQuery } from '../../api/insights';
import UniversalTrackerModalService from '../../model/UniversalTrackerModalService';
import { UniversalTrackerModalTab } from '@g17eco/types/universalTrackerModal';
import NotAuthorised from '../not-authorised';
import { API } from '../../constants/errors';
import { selectMostRecentSurveyByPeriodAndType } from '../../slice/initiativeSurveyListSlice';
import { useGetSurveyByIdQuery } from '../../api/surveys';
import { useGetAvailablePeriodsQuery } from '../../api/initiatives';
import { TOOLTIP_MESSAGE } from '../summary/insights/utils/sidebar';
import { CustomDashboardWrapper } from './context/CustomDashboardWrapper';
import { BasicAlert } from '@g17eco/molecules/alert';
import { LoadingPlaceholder } from '@g17eco/molecules/loader-container';
import { Filters as SurveyFilters } from '@features/utr-modal/SurveyFilters';
import { UtrvHistoryModalTemplate } from '@features/utr-modal/UtrvHistoryModalTemplate';
import { UtrModalBody } from '@features/utr-modal/UtrModalBody';
import { DateRangeType } from '@g17eco/types/common';
import { getDeletedDocumentIds, getNewFilesToUpload, mapFilesToItems } from '@features/custom-dashboard';
import {
  dashbboardMetricStatusOptions,
  DEFAULT_FILTERS,
  getTimeFramePeriod,
  getTimeFrameType,
} from './dashboard-settings/utils';
import {
  useInsightDashboardFilters,
  usePresetInsightDashboardFilters,
} from '@features/custom-dashboard/hooks/useInsightDashboardFilters';
import { generateErrorToast, generateToast } from '@g17eco/molecules/toasts';
import { useCTCustomDashboards } from '@hooks/useCTCustomDashboards';
import { EditingDashboardItem } from '@features/custom-dashboard/types';
import { isIntegratedDashboard } from '@features/custom-dashboard/utils/dashboard-utils';
import { getMainDownloadCode } from '../../config/app-config';
import { getRootOrg } from '../../selectors/initiative';
import { UniversalTrackerModalServiceUtrv } from '@g17eco/types/universalTrackerValue';

export const CustomDashboardContainer = () => {
  const history = useHistory();
  const { initiativeId, dashboardId } = useParams<{ initiativeId: string; dashboardId: string }>();
  const rootInitiative = useAppSelector(getRootOrg);
  const mainDownloadCode = getMainDownloadCode(rootInitiative?.appConfigCode, rootInitiative?.permissionGroup);

  const { filters, setFilters } = useInsightDashboardFilters({ period: DEFAULT_FILTERS.period });
  const { period, timeFrame, dateRange } = filters;

  const {
    data: dashboard,
    isFetching: isFetchingDashboard,
    isError: isErrorDashboard,
    error: errorDashboard,
    refetch: refetchDashboard,
    // TODO: Should get the data based on subsidiaries filter
  } = useGetInsightDashboardByIdQuery({
    dashboardId,
    initiativeId,
    queryParams: { period, timeFrameType: timeFrame, dateRange },
  });

  const surveyType = dashboard?.filters.surveyType ?? DEFAULT_FILTERS.surveyType;

  const { data: availablePeriods = [] } = useGetAvailablePeriodsQuery(
    {
      initiativeId,
      surveyType,
    },
    { skip: !dashboard },
  );

  const [uploadFiles, { isLoading: isUploading }] = useUploadInsightDashboardMediaFilesMutation();

  const rootConfig = useAppSelector(getRootConfig);
  const canAccessIntegrations = FeaturePermissions.canAccessAppIntegrations(rootConfig);
  const { data: integrationProviders } = useGetInsightDashboardIntegrationsQuery(initiativeId, {
    skip: !canAccessIntegrations,
  });
  const [getHistoricalUtrsByCodes, { isLoading: isLoadingUtrsData }] = useLazyGetHistoricalUtrsByCodesQuery();

  const canAccessCustomDashboards = FeaturePermissions.canAccessCustomDashboards(rootConfig);
  const initiative = useAppSelector((state: RootState) => state.initiative.data);
  const currentUser = useAppSelector(getCurrentUser);
  const mostRecentSurvey = useAppSelector((state: RootState) =>
    selectMostRecentSurveyByPeriodAndType(state, period, surveyType),
  );

  const { data: latestSurvey } = useGetSurveyByIdQuery(mostRecentSurvey?._id ?? '', {
    skip: !mostRecentSurvey?._id,
  });

  const [selectingUtrData, setSelectingUtrData] = useState<UniversalTrackerModalService | undefined>(undefined);
  const [firstValueListCode, setFirstValueListCode] = useState<string | undefined>(undefined);
  const [editingItem, setEditingItem] = useState<EditingDashboardItem | undefined>(undefined);

  const isCurrentLevelDashboard = initiativeId === dashboard?.initiativeId;
  const canManage = useAppSelector(canManageCurrentLevel);

  usePresetInsightDashboardFilters({ filters, setFilters, dashboard });

  const ctDashboardProps = useCTCustomDashboards({ initiativeId, dashboardId, mainDownloadCode });
  const {
    currentPage,
    options,
    isDataLoading,
    isEditing,
    setIsEditing,
    handleClickOption,
    handleUpdateDashboard,
    handleDuplicateDashboard,
    handleDeleteDashboard,
    toggleManageModal,
    isLoading: isLoadingDashboards,
    error: errorDashboards,
  } = ctDashboardProps;

  const commonSidebarProps = {
    initiativeId,
    availablePeriods,
    ...ctDashboardProps,
  };

  if (errorDashboard?.message === API.NOT_PERMITTED) {
    return <NotAuthorised />;
  }

  if (!dashboard || !initiative || isDataLoading) {
    return (
      <Dashboard className='profile-dashboard insights-dashboard' hasSidebar>
        <CompanyInsightsSidebar {...commonSidebarProps} />
        <LoadingPlaceholder height={600} />
      </Dashboard>
    );
  }

  const handleGetHistoricalUtrsByCodes = (params: HistoricalUtrsQueryByCodeParams): Promise<HistoricalUtrs[]> => {
    return getHistoricalUtrsByCodes(params).unwrap();
  };

  const handleClickEdit = () => setIsEditing(true);
  const handleCancel = () => setIsEditing(false);

  const handleSave = async (changes: Partial<InsightDashboard>, keepEditing?: boolean) => {
    const newItems = changes.items ?? dashboard.items; // Fallback to existing dashboard items when saving settings to avoid clear all items.
    const newFiles = getNewFilesToUpload(newItems);

    const uploadedFiles = Object.keys(newFiles).length
      ? await uploadFiles({ files: newFiles, initiativeId, dashboardId }).unwrap()
      : [];

    // Map media back to items with new url, documentId properties.
    const itemsWithMedia = mapFilesToItems(uploadedFiles, newItems);

    const deletedDocumentIds = getDeletedDocumentIds(dashboard.items, newItems);
    await handleUpdateDashboard({ ...dashboard, ...changes, items: itemsWithMedia, initiativeId, deletedDocumentIds });

    if (!keepEditing) {
      setIsEditing(false);
    }

    // Sync with surveyPeriod, surveyType and timeFrame
    if (changes.filters) {
      const { type: timeFrame, startDate, endDate } = changes.filters.timeFrame ?? DEFAULT_FILTERS.timeFrame;
      setFilters({
        period: changes.filters?.period ?? DEFAULT_FILTERS.period,
        timeFrame: timeFrame,
        dateRange: { startDate, endDate },
      });
    }
  };

  const generateSelectingUtrData = ({
    utrData,
    activeTab,
  }: {
    utrData: HistoricalUtrs;
    activeTab?: UniversalTrackerModalTab['navId'];
  }) => {
    const modalService = new UniversalTrackerModalService(utrData.utr);
    modalService.setUniversalTrackerValues(utrData.utrvs);
    modalService.setInitiativeId(initiativeId || dashboard.initiativeId);
    modalService.setActiveTabId(activeTab);

    setSelectingUtrData(modalService);
  };

  const handleOpenUtrvHistoryModal = ({
    item,
    utrData,
    activeTab,
  }: {
    item: GridDashboardItem;
    utrData?: HistoricalUtrs;
    activeTab?: UniversalTrackerModalTab['navId'];
  }) => {
    if (!hasUtrvHistoryModal(item) || !utrData) {
      return;
    }
    setFirstValueListCode(getFirstValueListCode(item));
    generateSelectingUtrData({ utrData, activeTab });
  };

  const reloadUtrvs = async (filters: SurveyFilters) => {
    await handleChangeUtrvHistoryModalFilters(filters);
    await refetchDashboard();
  };

  const handleChangeUtrvHistoryModalFilters = (filters: SurveyFilters) => {
    if (!selectingUtrData) {
      return;
    }

    const queryParams: DashboardItemFilters = {
      ...dashboard.filters,
      ...filters,
    };

    return handleGetHistoricalUtrsByCodes({
      initiativeId,
      utrCodes: [selectingUtrData.getUniversalTracker().getCode()],
      queryParams,
    }).then((result) => {
      if (!result[0]) {
        return;
      }

      return generateSelectingUtrData({ utrData: result[0] });
    });
  };

  const handleChangeDateRange = (dateRange: DateRangeType, timeFrame: string | number) => {
    setFilters({ timeFrame: getTimeFrameType(timeFrame), dateRange });
  };

  const redirectUtrvSource = (utrv: UniversalTrackerModalServiceUtrv) => {
    if (!utrv.compositeData?.surveyId) {
      return;
    }
    const surveyPath = generateUrl(ROUTES.COMPANY_TRACKER_SURVEY, {
      initiativeId,
      surveyId: utrv.compositeData.surveyId,
      page: 'question',
    });
    const url = `${surveyPath}/${utrv._id}`;
    history.push(url);
  };

  const isLoading = isFetchingDashboard || isLoadingDashboards || isLoadingUtrsData || isUploading;
  const isError = isErrorDashboard || Boolean(errorDashboards);

  const renderCustomDashboard = () => {
    if (!canAccessCustomDashboards && !isIntegratedDashboard(dashboard.type)) {
      return (
        <Dashboard className='w-100'>
          <DashboardSection>
            <DashboardSectionTitle title={TOOLTIP_MESSAGE.NOT_AVAILABLE_PLAN} />
          </DashboardSection>
        </Dashboard>
      );
    }

    return (
      <CustomDashboardWrapper
        integrationProviders={integrationProviders}
        getHistoricalUtrsByCodes={handleGetHistoricalUtrsByCodes}
        duplicateDashboard={() => handleDuplicateDashboard(dashboardId)}
        redirectUtrvSource={redirectUtrvSource}
      >
        {isLoading ? <Loader /> : null}
        {selectingUtrData ? (
          <UtrvHistoryModalTemplate toggle={() => setSelectingUtrData(undefined)}>
            <UtrModalBody
              canAccessCustomDashboards={canAccessCustomDashboards}
              reloadUtrvs={reloadUtrvs}
              modalService={selectingUtrData}
              firstValueListCode={firstValueListCode}
              currentUser={currentUser}
              canManage={canManage}
              disableTargetBaselineAdding={isEditing}
              activeTabId={selectingUtrData.getActiveTabId()}
              initialFilters={{ surveyType, period: period ?? 'all' }}
              onChangeFilters={handleChangeUtrvHistoryModalFilters}
              isOpenFilterByDefault
            />
          </UtrvHistoryModalTemplate>
        ) : null}
        {isEditing ? (
          <CustomDashboardEditing
            editingItem={editingItem}
            setEditingItem={setEditingItem}
            dashboard={dashboard}
            handleCancel={handleCancel}
            handleSave={handleSave}
            handleDelete={() => handleDeleteDashboard(dashboardId)}
            handleOpenUtrvHistoryModal={handleOpenUtrvHistoryModal}
            initiative={initiative}
            survey={latestSurvey}
            availablePeriods={availablePeriods}
            period={period}
            surveyType={surveyType}
            metricStatusOptions={dashbboardMetricStatusOptions}
            rootOrg={rootInitiative}
            dataFilters={{ period, dateRange, timeRange: getTimeFramePeriod(timeFrame) }}
          />
        ) : (
          <CustomDashboard
            setEditingItem={setEditingItem}
            dashboard={dashboard}
            handleClickEdit={handleClickEdit}
            handleSave={handleSave}
            handleDelete={() => handleDeleteDashboard(dashboardId)}
            canManage={canManage}
            isCurrentLevelDashboard={isCurrentLevelDashboard}
            handleOpenUtrvHistoryModal={handleOpenUtrvHistoryModal}
            initiative={initiative}
            survey={latestSurvey}
            availablePeriods={availablePeriods}
            period={period}
            setPeriod={(period) => setFilters({ period })}
            dateRange={dateRange}
            onChangeDateRange={handleChangeDateRange}
            timeRange={getTimeFramePeriod(timeFrame)}
            currentPage={currentPage}
            dashboardOptions={options}
            handleClickOption={handleClickOption}
            metricStatusOptions={dashbboardMetricStatusOptions}
            rootOrg={rootInitiative}
            toggleManageModal={toggleManageModal}
          />
        )}
      </CustomDashboardWrapper>
    );
  };

  return (
    <Dashboard className='profile-dashboard insights-dashboard' hasSidebar>
      <CompanyInsightsSidebar key={`company-insights-sidebar-${isEditing}`} {...commonSidebarProps} />
      {isError ? <BasicAlert type={'danger'}>Something wrong</BasicAlert> : null}
      {renderCustomDashboard()}
    </Dashboard>
  );
};
