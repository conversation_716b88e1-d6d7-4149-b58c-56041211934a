import { Option, SelectFactory, SelectTypes } from '@g17eco/molecules/select/SelectFactory';
import { InsightDashboardOption, MANAGE_DASHBOARD_OPTION } from '@routes/summary/insights/utils/sidebar';
import classNames from 'classnames';

interface Props {
  title: string | JSX.Element;
  currentPage: string;
  options: InsightDashboardOption[];
  className?: string;
  handleClickOption: (item: InsightDashboardOption) => void;
  toggleManageModal?: () => void;
}

export const CustomDashboardTitle = ({
  title,
  currentPage,
  options,
  handleClickOption,
  className = '',
  toggleManageModal = () => {},
}: Props) => {
  const selectedItem = options.find((i) => i.value === currentPage) ?? { label: title, value: null };

  const titleOptions = [...options, MANAGE_DASHBOARD_OPTION];

  const onSelect = (option: Option | null) => {
    const item = titleOptions.find((o) => o.value === option?.value);
    if (!item) {
      return;
    }
    if (item.value === MANAGE_DASHBOARD_OPTION.value) {
      toggleManageModal();
      return;
    }
    handleClickOption(item);
  };

  return (
    <>
      <div className={classNames('d-inline-block d-xxl-none', className)}>
        <SelectFactory
          value={{
            label: <span className='h3'>{selectedItem.label}</span>,
            value: selectedItem.value,
          }}
          selectType={SelectTypes.SingleSelect}
          placeholder='Select a custom dashboard'
          options={titleOptions}
          onChange={(option) => onSelect(option)}
          isSearchable={false}
          isTransparent
        />
      </div>
      <div className={classNames('d-none d-xxl-inline-flex align-items-center', className)}>{title}</div>
    </>
  );
};
