import { Button } from 'reactstrap';
import {
  InsightDashboard,
  InsightDashboardFilters,
  InsightDashboardSettingKeys,
  MetricStatusOption,
} from '@g17eco/types/insight-custom-dashboard';
import { useToggle } from '@hooks/useToggle';
import { DashboardSettingsSidebar } from './DashboardSettingsSidebar';
import type { DashboardSettingsProps } from '@features/custom-dashboard';
import { DataPeriods } from '@utils/dataPeriods';
import { useEffect } from 'react';

interface Props {
  dashboard: Pick<InsightDashboard, 'title' | 'filters' | 'type' | 'items' | 'initiativeId'>;
  handleSave: (dashboard: Partial<InsightDashboard>, keepEditing?: boolean) => void;
  handleDelete?: () => void;
  hideOptions?: InsightDashboardSettingKeys[];
  availablePeriods: DataPeriods[];
  metricStatusOptions?: MetricStatusOption[];
  disabled?: boolean;
  components?: {
    [key in InsightDashboardFilters]?: (props: DashboardSettingsProps) => React.ReactNode;
  };
  isPresetOpenSidebar?: boolean;
}

export const DashboardSettings = ({
  dashboard,
  hideOptions = [],
  availablePeriods,
  handleSave,
  handleDelete = () => {},
  metricStatusOptions,
  disabled = false,
  components,
  isPresetOpenSidebar = false,
}: Props) => {
  const [openSettingsSidebar, toggleSettingsSidebar, setOpenSettingsSidebar] = useToggle();
  const dashboardKey = '_id' in dashboard ? dashboard._id : dashboard.type;

  useEffect(() => {
    if (isPresetOpenSidebar) {
      setOpenSettingsSidebar(true);
    }
  }, [isPresetOpenSidebar]);

  return (
    <>
      <Button
        tooltip='Select the data being pulled'
        color='secondary'
        className='px-2 ml-2'
        onClick={() => setOpenSettingsSidebar(true)}
        disabled={disabled}
      >
        <i className='fa-light fa-gear fs-6 mr-1' /> Settings
      </Button>
      <DashboardSettingsSidebar
        key={`${dashboard.initiativeId}-${dashboardKey} ${openSettingsSidebar}`}
        isOpenSidebar={openSettingsSidebar}
        toggleSidebar={toggleSettingsSidebar}
        dashboard={dashboard}
        handleSave={handleSave}
        handleDelete={handleDelete}
        hideOptions={hideOptions}
        availablePeriods={availablePeriods}
        metricStatusOptions={metricStatusOptions}
        components={components}
      />
    </>
  );
};
