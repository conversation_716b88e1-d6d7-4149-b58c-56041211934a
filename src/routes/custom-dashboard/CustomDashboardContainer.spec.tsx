import { describe, it, expect, vi, beforeEach, type Mock } from 'vitest';
import { screen, setup } from '@fixtures/utils';
import { CustomDashboardContainer } from './CustomDashboardContainer';
import { initiativeOne } from '@fixtures/initiative-factory';
import { createSurveyData } from '@fixtures/survey-factory';
import { createInsightDashboard } from '@fixtures/custom-dashboard-factory';
import { configureStore } from '@reduxjs/toolkit';
import { reducer } from '@reducers/index';
import { getCurrentUserState, userOne } from '@fixtures/user-factory';
import { reduxMiddleware } from '@fixtures/redux-store';
import { InsightDashboardType } from '../../types/insight-custom-dashboard';
import { useGetInsightDashboardByIdQuery, useGetInsightDashboardsByInitiativeQuery } from '@api/insight-dashboards';
import { useCTCustomDashboards } from '@hooks/useCTCustomDashboards';
import { API } from '@constants/errors';

const mockPush = vi.fn();
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useParams: () => ({
      initiativeId: initiativeOne._id,
      dashboardId: 'dashboard-123',
    }),
    useHistory: () => ({
      push: mockPush,
    }),
    useLocation: () => ({ search: '' }),
  };
});

vi.mock('@routes/not-authorised', () => {
  const MockNotAuthorised = vi.fn(() => <div data-testid='not-authorised'>Not Authorised</div>);
  return { __esModule: true, default: MockNotAuthorised };
});

vi.mock('@g17eco/molecules/toasts', () => ({
  generateToast: vi.fn(),
  generateErrorToast: vi.fn(),
}));

vi.mock('@hooks/useCTCustomDashboards', () => ({
  useCTCustomDashboards: vi.fn(),
}));

vi.mock('./dashboard-settings/useSettingsSidebar', () => ({
  useSettingsSidebar: () => ({
    handleAddNew: vi.fn(),
  }),
}));

const defaultCTCustomDashboards = {
  currentPage: 'custom-dashboard',
  options: [],
  isDataLoading: false,
  isEditing: false,
  setIsEditing: vi.fn(),
  handleClickOption: vi.fn(),
  handleNavigateCustom: vi.fn(),
  handleUpdateDashboard: vi.fn(),
  updateDashboardState: { isLoading: false, isError: false },
  handleDuplicateDashboard: vi.fn(),
  duplicateDashboardState: { isLoading: false, isError: false },
  handleDeleteDashboard: vi.fn(),
  deleteDashboardState: { isLoading: false, isError: false },
  updatePreferencesState: { isLoading: false, isError: false },
  toggleManageModal: vi.fn(),
  dashboardOptions: [],
  preferences: [],
  templates: [],
  isManageModalOpen: false,
  manageDashboardBtn: null,
  showMoreDashboardsBtn: null,
  handleSavePreferences: vi.fn(),
  handleCreateTemplate: vi.fn(),
  handleAddNew: vi.fn(),
};

// Mock API hooks with default values
const defaultDashboardQuery = {
  data: null,
  isFetching: false,
  isError: false,
  error: undefined,
  refetch: vi.fn(),
};

vi.mock('@api/insights', () => ({
  useLazyGetHistoricalUtrsByCodesQuery: () => [vi.fn(() => Promise.resolve([])), { isLoading: false }],
}));

vi.mock('@api/initiatives', () => ({
  useGetAvailablePeriodsQuery: () => ({
    data: ['yearly', 'quarterly'],
    isFetching: false,
  }),
}));

vi.mock('@api/surveys', () => ({
  useGetSurveyByIdQuery: () => ({
    data: null,
    isFetching: false,
  }),
}));

vi.mock('@api/insight-dashboards', async () => {
  const actual = await vi.importActual('@api/insight-dashboards');
  return {
    ...actual,
    useGetInsightDashboardByIdQuery: vi.fn(),
    useGetInsightDashboardIntegrationsQuery: () => ({
      data: [],
      isFetching: false,
    }),
    useDeleteInsightDashboardMutation: () => [vi.fn(), { isLoading: false, isError: false }],
    useDuplicateInsightDashboardMutation: () => [vi.fn(), { isLoading: false, isError: false }],
    useUpdateInsightDashboardMutation: () => [vi.fn(), { isLoading: false, isError: false }],
    useUploadInsightDashboardMediaFilesMutation: () => [vi.fn(), { isLoading: false }],
    useCreateInsightDashboardMutation: () => [vi.fn(), { isLoading: false }],
    useGetInsightDashboardsByInitiativeQuery: vi.fn(),
    useGetInsightDashboardTemplatesQuery: () => ({
      data: [],
      isFetching: false,
    }),
    useCreateTemplateDashboardMutation: () => [vi.fn(), { isLoading: false }],
  };
});

describe('CustomDashboardContainer', () => {
  const mockSurvey = createSurveyData({ _id: 'survey-123' });
  const mockDashboard = createInsightDashboard({
    _id: 'dashboard-123',
    initiativeId: initiativeOne._id,
    title: 'Test Dashboard',
    type: InsightDashboardType.Custom,
  });

  const createStore = () =>
    configureStore({
      reducer,
      preloadedState: {
        currentUser: getCurrentUserState(userOne),
        initiative: {
          data: initiativeOne,
          isFetching: false,
          error: undefined,
        },
        initiativeSurveyList: {
          data: [mockSurvey],
          isFetching: false,
          error: undefined,
        },
        globalData: {
          data: {
            config: {
              features: [{ code: 'custom_dashboards', name: 'Custom Dashboards', active: true }],
            },
          },
          loaded: true,
          isFetching: false,
          error: undefined,
        },
      },
      middleware: reduxMiddleware,
    });

  const renderComponent = () => {
    const store = createStore();
    return setup(<CustomDashboardContainer />, { store });
  };

  beforeEach(() => {
    // Reset mock state before each test
    vi.clearAllMocks();
    (useGetInsightDashboardByIdQuery as Mock).mockReturnValue(defaultDashboardQuery);
    (useGetInsightDashboardsByInitiativeQuery as Mock).mockReturnValue({
      data: [],
      isFetching: false,
    });
    (useCTCustomDashboards as Mock).mockReturnValue(defaultCTCustomDashboards);
  });

  describe('loading states', () => {
    it('shows loading placeholder when dashboard data is not available', () => {
      renderComponent();
      expect(screen.getByTestId('loading-placeholder')).toBeInTheDocument();
    });

    it('shows loading placeholder while fetching dashboards', () => {
      (useGetInsightDashboardByIdQuery as Mock).mockReturnValue({
        ...defaultDashboardQuery,
        data: mockDashboard,
      });

      (useCTCustomDashboards as Mock).mockReturnValue({
        ...defaultCTCustomDashboards,
        isDataLoading: true,
      });

      renderComponent();
      expect(screen.getByTestId('loading-placeholder')).toBeInTheDocument();
    });
  });

  describe('error states', () => {
    it('shows error alert when general error occurs', () => {
      (useGetInsightDashboardByIdQuery as Mock).mockReturnValue({
        ...defaultDashboardQuery,
        isError: true,
        error: { message: API.NOT_PERMITTED },
      });

      renderComponent();
      expect(screen.getByTestId('not-authorised')).toBeInTheDocument();
    });
  });

  describe('rendering with data', () => {
    beforeEach(() => {
      (useGetInsightDashboardByIdQuery as Mock).mockReturnValue({
        ...defaultDashboardQuery,
        data: mockDashboard,
        isFetching: false,
      });
    });

    it('renders dashboard without loading placeholder when data is available', () => {
      renderComponent();
      expect(screen.queryByTestId('loading-placeholder')).not.toBeInTheDocument();
    });

    it('renders dashboard with sidebar structure', () => {
      const { container } = renderComponent();
      const dashboardContainer = container.querySelector('.insights-dashboard');
      expect(dashboardContainer).toBeInTheDocument();
      expect(dashboardContainer).toHaveClass('has-sidebar');
    });

    it('renders dashboard in editing mode when isEditing is true', () => {
      (useCTCustomDashboards as Mock).mockReturnValue({
        ...defaultCTCustomDashboards,
        isEditing: true,
      });

      const { container } = renderComponent();

      // Dashboard should be rendered in editing mode
      expect(container.querySelector('.insights-dashboard')).toBeInTheDocument();

      // Verify dashboard renders without errors in editing mode
      expect(screen.queryByTestId('loading-placeholder')).not.toBeInTheDocument();
    });

    it('renders template dashboard type', () => {
      const templateDashboard = createInsightDashboard({
        _id: 'dashboard-456',
        initiativeId: initiativeOne._id,
        title: 'Template Dashboard',
        type: InsightDashboardType.WFNTemplate,
      });

      (useGetInsightDashboardByIdQuery as Mock).mockReturnValue({
        ...defaultDashboardQuery,
        data: templateDashboard,
        isFetching: false,
      });

      renderComponent();

      // Template dashboards (integrated type) should bypass permission checks
      // and not show "Not available on your current plan" message
      expect(screen.queryByText(/not available on your current plan/i)).not.toBeInTheDocument();
      expect(screen.queryByTestId('loading-placeholder')).not.toBeInTheDocument();
    });
  });
});
