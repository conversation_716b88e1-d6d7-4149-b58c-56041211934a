/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import { Fragment } from 'react';
import { Button, Collapse } from 'reactstrap';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';
import { ellipsis } from '@utils/index';
import { CustomDashboardInfoIcon } from '@features/custom-dashboard';
import { InsightDashboardOption, TOOLTIP_MESSAGE } from '../../utils/sidebar';
import classNames from 'classnames';

const TRUNCATED_EXPAND_LIMIT = 11;
const TRUNCATED_TEXT_LIMIT = 30;

/**
 * If updates happen to sidebar, should check the updates in these places:
 * StaticDashboardRoute, CustomDashboardContainer, PortfolioCompanyInsights
 * StaticPortfolioInsights, PortfolioCustomDashboardRoute
 */

interface SidebarProps {
  options: InsightDashboardOption[];
  currentPage: string;
  className?: string;
  handleClickOption: (item: InsightDashboardOption) => void;
  manageDashboardBtn: JSX.Element | null;
  showMoreDashboardsBtn: JSX.Element | null;
}

export const InsightsSidebar = (props: SidebarProps) => {
  const { options, currentPage, handleClickOption, manageDashboardBtn, showMoreDashboardsBtn } = props;

  const getOption = (item: InsightDashboardOption) => {
    return item.isCustom && typeof item.label === 'string' ? (
      <SimpleTooltip text={item.tooltip ?? item.label}>
        <Button
          color={item.color ?? 'transparent'}
          disabled={item.disabled && !item.canView}
          onClick={() => handleClickOption(item)}
          className={classNames(item.className, 'px-1')}
        >
          {ellipsis(item.label, TRUNCATED_TEXT_LIMIT)}
        </Button>
      </SimpleTooltip>
    ) : (
      <SimpleTooltip text={item.tooltip}>
        <Button
          color={item.color ?? 'transparent'}
          disabled={item.disabled}
          onClick={() => handleClickOption(item)}
          className={classNames(item.className, 'px-1')}
        >
          {item.label}
        </Button>
      </SimpleTooltip>
    );
  };
  return (
    <div className='d-none d-xxl-inline-block insights-sidebar-container settings-sidebar-container'>
      <div className='sticky-sidebar'>
        {options.map((item) => (
          <Fragment key={`sidebar_${item.value}`}>
            {item.value !== currentPage ? (
              <div className='d-flex align-items-center'>
                <div className='flex-grow-1'>{getOption(item)}</div>
                <CustomDashboardInfoIcon text={item.isSharedByParent ? TOOLTIP_MESSAGE.IS_SHARED_BY_PARENT : ''} />
              </div>
            ) : null}
            <Collapse isOpen={item.value === currentPage} className='sidebar-title strong'>
              <span className='my-3'>
                {item.isCustom && typeof item.label === 'string' ? (
                  <SimpleTooltip text={item.label}>{ellipsis(item.label, TRUNCATED_EXPAND_LIMIT)}</SimpleTooltip>
                ) : (
                  item.label
                )}
              </span>
            </Collapse>
          </Fragment>
        ))}
        {showMoreDashboardsBtn ? <div className='mt-2'>{showMoreDashboardsBtn}</div> : null}
        {manageDashboardBtn ? <div className='mt-2'>{manageDashboardBtn}</div> : null}
      </div>
    </div>
  );
};
