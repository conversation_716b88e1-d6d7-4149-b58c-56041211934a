import { useCreatePortfolioInsightDashboardMutation } from '@api/portfolio-insight-dashboards';
import { InsightsSidebar } from './InsightsSidebar';
import { useSiteAlert } from '@hooks/useSiteAlert';
import { Loader } from '@g17eco/atoms/loader';
import { DashboardSettingsSidebar } from '@routes/custom-dashboard/dashboard-settings/DashboardSettingsSidebar';
import { DEFAULT_FILTERS } from '@routes/custom-dashboard/dashboard-settings/utils';
import {
  GridDashboardItem,
  InsightDashboard,
  InsightDashboardActions,
  InsightDashboardFilters,
  InsightDashboardItemType,
  InsightDashboardType,
  OutputHookCustomDashboards,
} from '@g17eco/types/insight-custom-dashboard';
import { generateGridDashboardItem } from '@routes/custom-dashboard/utils';
import { DefaultInsightViewSwitcher } from '@features/custom-dashboard/dashboard-settings/DashboardSettings';
import { ManageDashboardsModal } from '@routes/custom-dashboard/manage-dashboards/ManageDashboardsModal';

interface PortfolioInsightsSidebarProps
  extends Pick<
    OutputHookCustomDashboards,
    | 'currentPage'
    | 'dashboardOptions'
    | 'options'
    | 'openSettingsSidebar'
    | 'toggleSettingsSidebar'
    | 'handleClickOption'
    | 'handleNavigateCustom'
    | 'isManageModalOpen'
    | 'dashboards'
    | 'preferences'
    | 'isUpdatingPreferences'
    | 'manageDashboardBtn'
    | 'showMoreDashboardsBtn'
    | 'toggleManageModal'
    | 'handleAddNew'
    | 'handleSavePreferences'
    | 'handleDuplicateDashboard'
    | 'handleDeleteDashboard'
  > {
  portfolioId: string;
}

const DEFAULT_PORTFOLIO_FILTERS = {
  ...DEFAULT_FILTERS,
  shareWithSubsidiaries: {
    enabled: false,
  },
  displayAsDefault: {
    enabled: true,
  },
};

export const PortfolioInsightsSidebar = ({
  portfolioId,
  currentPage,
  options,
  openSettingsSidebar,
  toggleSettingsSidebar,
  handleClickOption,
  handleNavigateCustom,
  isManageModalOpen,
  dashboardOptions,
  preferences,
  isUpdatingPreferences = false,
  manageDashboardBtn,
  showMoreDashboardsBtn,
  toggleManageModal,
  handleAddNew,
  handleSavePreferences,
  handleDuplicateDashboard,
  handleDeleteDashboard,
}: PortfolioInsightsSidebarProps) => {
  const { addSiteError } = useSiteAlert();

  const [createInsightDashboard, { isLoading }] = useCreatePortfolioInsightDashboardMutation();

  const handleSave = async (changes: Partial<InsightDashboard>) => {
    if (!portfolioId) {
      return;
    }

    const items: GridDashboardItem[] = [];
    if (changes.filters?.sdgContribution?.enabled) {
      const item = generateGridDashboardItem({ type: InsightDashboardItemType.SDGContributionChart }, items);
      items.push(item);
    }

    await createInsightDashboard({
      initiativeId: portfolioId,
      items,
      title: changes.title ?? 'Custom Dashboard',
      filters: changes.filters ?? DEFAULT_PORTFOLIO_FILTERS,
    })
      .unwrap()
      .then((dashboard) => {
        handleNavigateCustom(dashboard._id);
      })
      .catch((e) => {
        addSiteError(e);
      });
  };

  return (
    <>
      {isLoading ? <Loader /> : null}
      <InsightsSidebar
        options={options}
        currentPage={currentPage}
        handleClickOption={handleClickOption}
        showMoreDashboardsBtn={showMoreDashboardsBtn}
        manageDashboardBtn={manageDashboardBtn}
      />
      <DashboardSettingsSidebar
        key={`${portfolioId} ${currentPage} ${openSettingsSidebar}`}
        isOpenSidebar={openSettingsSidebar}
        toggleSidebar={toggleSettingsSidebar}
        dashboard={{
          title: 'Custom Dashboard',
          initiativeId: portfolioId,
          filters: DEFAULT_PORTFOLIO_FILTERS,
          type: InsightDashboardType.Custom,
          items: [],
        }}
        handleSave={handleSave}
        hideOptions={[
          InsightDashboardFilters.ShareWithSubsidiaries,
          InsightDashboardFilters.BaselinesTargets,
          InsightDashboardFilters.Period,
          InsightDashboardFilters.Survey,
          InsightDashboardFilters.Privacy,
          InsightDashboardActions,
          InsightDashboardFilters.InitiativeIds,
        ]}
        availablePeriods={[]}
        components={{
          [InsightDashboardFilters.DisplayAsDefault]: DefaultInsightViewSwitcher,
        }}
      />
      <ManageDashboardsModal
        key={`manage-dashboards-modal-${isManageModalOpen}`}
        isOpen={isManageModalOpen}
        toggle={toggleManageModal}
        initiativeId={portfolioId}
        dashboardOptions={dashboardOptions}
        initialPreferences={preferences}
        isUpdatingPreferences={isUpdatingPreferences}
        handleSavePreferences={handleSavePreferences}
        handleDuplicateDashboard={handleDuplicateDashboard}
        handleDeleteDashboard={handleDeleteDashboard}
        handleAddNew={handleAddNew}
        templateOptions={[]}
      />
    </>
  );
};
