/*
 * Copyright (c) 2019. World Wide Generation Ltd
 */

import { useCreateInsightDashboardMutation } from '@api/insight-dashboards';
import { InsightsSidebar } from './InsightsSidebar';
import { Loader } from '@g17eco/atoms/loader';
import {
  GridDashboardItem,
  InsightDashboard,
  InsightDashboardActions,
  InsightDashboardItemType,
  InsightDashboardType,
  OutputHookCustomDashboards,
} from '@g17eco/types/insight-custom-dashboard';
import { dashbboardMetricStatusOptions, DEFAULT_FILTERS } from '@routes/custom-dashboard/dashboard-settings/utils';
import { DashboardSettingsSidebar } from '@routes/custom-dashboard/dashboard-settings/DashboardSettingsSidebar';
import { generateGridDashboardItem } from '@routes/custom-dashboard/utils';
import { DataPeriods } from '@utils/dataPeriods';
import { UtrvFilter } from '@g17eco/types/insight-custom-dashboard-filter';
import { ManageDashboardsModal } from '@routes/custom-dashboard/manage-dashboards/ManageDashboardsModal';

interface CompanyInsightsSidebarProps
  extends Pick<
    OutputHookCustomDashboards,
    | 'currentPage'
    | 'options'
    | 'openSettingsSidebar'
    | 'toggleSettingsSidebar'
    | 'handleClickOption'
    | 'handleNavigateCustom'
    | 'isManageModalOpen'
    | 'dashboardOptions'
    | 'preferences'
    | 'isUpdatingPreferences'
    | 'templates'
    | 'manageDashboardBtn'
    | 'showMoreDashboardsBtn'
    | 'toggleManageModal'
    | 'handleAddNew'
    | 'handleSavePreferences'
    | 'handleCreateTemplate'
    | 'handleDuplicateDashboard'
    | 'handleDeleteDashboard'
  > {
  initiativeId: string;
  availablePeriods: DataPeriods[];
}

export const CompanyInsightsSidebar = (props: CompanyInsightsSidebarProps) => {
  const {
    initiativeId,
    availablePeriods,
    currentPage,
    options,
    openSettingsSidebar,
    toggleSettingsSidebar,
    handleClickOption,
    handleNavigateCustom = () => {},
    isManageModalOpen,
    dashboardOptions,
    preferences,
    isUpdatingPreferences = false,
    templates,
    manageDashboardBtn,
    showMoreDashboardsBtn,
    toggleManageModal,
    handleSavePreferences,
    handleDuplicateDashboard,
    handleDeleteDashboard,
    handleCreateTemplate,
    handleAddNew,
  } = props;

  const [createInsightDashboard, { isLoading }] = useCreateInsightDashboardMutation();

  const handleSave = async (changes: Partial<InsightDashboard>) => {
    if (!initiativeId) {
      return;
    }
    const items: GridDashboardItem[] = [];
    if (changes.filters?.sdgContribution?.enabled) {
      const item = generateGridDashboardItem({ type: InsightDashboardItemType.SDGContributionChart }, items);
      items.push(item);
    }

    const dashboard = await createInsightDashboard({
      initiativeId,
      items,
      title: changes.title ?? 'Custom Dashboard',
      filters: changes.filters ?? DEFAULT_FILTERS,
    }).unwrap();
    handleNavigateCustom(dashboard._id);
  };

  const defaultSurveyPeriod = !availablePeriods.includes(DEFAULT_FILTERS.period)
    ? availablePeriods[0]
    : DEFAULT_FILTERS.period;

  return (
    <>
      {isLoading ? <Loader /> : null}
      <InsightsSidebar
        options={options}
        currentPage={currentPage}
        handleClickOption={handleClickOption}
        showMoreDashboardsBtn={showMoreDashboardsBtn}
        manageDashboardBtn={manageDashboardBtn}
      />
      <DashboardSettingsSidebar
        key={`${initiativeId} ${openSettingsSidebar}`}
        isOpenSidebar={openSettingsSidebar}
        toggleSidebar={toggleSettingsSidebar}
        dashboard={{
          title: 'Custom Dashboard',
          initiativeId,
          filters: { ...DEFAULT_FILTERS, period: defaultSurveyPeriod, utrv: UtrvFilter.AllAnswered },
          type: InsightDashboardType.Custom,
          items: [],
        }}
        handleSave={handleSave}
        // should hide actions when create a new dashboard
        hideOptions={[InsightDashboardActions]}
        availablePeriods={availablePeriods}
        metricStatusOptions={dashbboardMetricStatusOptions}
      />
      <ManageDashboardsModal
        key={`manage-dashboards-modal-${isManageModalOpen}`}
        isOpen={isManageModalOpen}
        toggle={toggleManageModal}
        initiativeId={initiativeId}
        dashboardOptions={dashboardOptions}
        initialPreferences={preferences}
        isUpdatingPreferences={isUpdatingPreferences}
        handleSavePreferences={handleSavePreferences}
        handleDuplicateDashboard={handleDuplicateDashboard}
        handleDeleteDashboard={handleDeleteDashboard}
        handleAddNew={handleAddNew}
        templateOptions={templates}
        handleCreateTemplate={handleCreateTemplate}
      />
    </>
  );
};
