import { Button } from 'reactstrap';
import { TOOLTIP_MESSAGE } from '../../utils/sidebar';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';

interface Props {
  canAccessCustomDashboards: boolean;
  isEditing?: boolean;
  toggleManageModal: () => void;
}

export const ManageDashboardButton = ({ canAccessCustomDashboards, isEditing, toggleManageModal }: Props) => {
  const getTooltip = () => {
    if (!canAccessCustomDashboards) {
      return TOOLTIP_MESSAGE.NOT_AVAILABLE_PLAN;
    }
    if (isEditing) {
      return TOOLTIP_MESSAGE.DISABLE_WHILE_EDITING;
    }
    return undefined;
  };

  return (
    <SimpleTooltip text={getTooltip()}>
      <Button
        color='link'
        className='insights-dashboard__manage-dashboard-btn px-3 py-1 mt-3'
        disabled={isEditing || !canAccessCustomDashboards}
        onClick={toggleManageModal}
      >
        <i className='fa-light fa-grid-2-plus fs-6 mr-2' />
        Manage dashboards
      </Button>
    </SimpleTooltip>
  );
};
