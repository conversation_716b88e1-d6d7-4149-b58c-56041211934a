export interface InsightDashboardOption {
  value: string;
  label: string | JSX.Element;
  isCustom?: boolean;
  disabled?: boolean;
  tooltip?: string;
  isSharedByParent?: boolean;
  isStaffOnly?: boolean;
  color?: string;
  className?: string;
  id?: string;
  icon?: string;
  subOptions?: InsightDashboardOption[];
  canView?: boolean;
}

export const TOOLTIP_MESSAGE = {
  NOT_AVAILABLE_PLAN: 'Not available on your current plan',
  DISABLE_WHILE_EDITING: 'Unable to manage dashboards while in edit mode',
  IS_SHARED_BY_PARENT:
    'Dashboard template will be made available to subsidiaries, but will be populated with their data. Any changes to this master dashboard will be pushed to subsidiaries automatically',
};

export const MANAGE_DASHBOARD_OPTION: InsightDashboardOption = {
  label: 'Manage dashboards',
  value: 'manage-dashboards',
};
