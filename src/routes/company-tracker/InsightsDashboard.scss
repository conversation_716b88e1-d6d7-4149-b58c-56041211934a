/*
 * Copyright (c) 2022. World Wide Generation Ltd
 */

@import "src/css/functions";


.insights-dashboard {

  @include media-breakpoint-up(xxl) {
    .insights-sidebar-container {
      margin-top: 3rem;
      margin-left: auto;
      text-align: right;
      .btn {
        text-transform: uppercase;
      }
      .sidebar-title {
        text-transform: uppercase;
        font-size: 2.4rem;
        transform: rotate(180deg);
        writing-mode: vertical-lr;
        color: var(--theme-TextLight);
      }
    }
  }

  &__manage-dashboard-btn, &__view-all-btn {
    text-transform: none !important;
  }

  .chart-or-not-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
  }
  .insights-question-reference {
    max-width: 100%;
    .chart-message {
      color: var(--theme-TextPlaceholder);
      .chart-message-utr {
        max-width: 100%;
        color: var(--theme-AccentMedium);
        .btn {
          max-width: 100%;
        }
      }
    }
  }

  .key-number {
    padding-top: 1.7rem;
    text-align: center;
    font-size: 2rem;
    font-weight: bold;
    color: var(--theme-HeadingLight);
    line-height: 2rem;
  }
  .key-number-unit {
    font-size: 0.7rem;
    color: var(--theme-TextPlaceholder);
  }

  .addbtn {
    position: absolute;
    bottom: 0;
    right: 0;
    .btn {
      color: var(--theme-IconSecondary);

      &.remove {
        right: -0.5rem; // Same as left 90% but works on bigger charts
      }
    }

    span {
      position: absolute;
      display: none;
      opacity: 0;
      height: 100%;
      right: 0px;
      margin-right: 5px;
      padding: 0.2rem 0.6rem;
      border: 1px solid var(--theme-BorderDefault);
      border-radius: $borderRadius;
      white-space: nowrap;
      -webkit-animation: slide 0.2s forwards;
      animation: slide 0.2s forwards;
      background-color: white;
      // box-shadow: 0 4px 8px 0 rgba(var(--theme-HeadingLight), 0.8);
      -webkit-box-shadow: 1px 1px 2px 2px rgba(0, 0, 0, 0.2);
      -moz-box-shadow: 1px 1px 2px 2px rgba(0, 0, 0, 0.2);
      box-shadow: 1px 1px 2px 2px rgba(0, 0, 0, 0.2);
    }
    &:hover {
      .btn {
        color: var(--theme-AccentMedium);
      }
      span {
        display: inline-block;
        -webkit-animation: slide 0.2s forwards;
        animation: slide 0.2s forwards;
      }
    }
  }

  @-webkit-keyframes slide {
    100% {
      right: 2rem;
      opacity: 100%;
    }
  }

  @keyframes slide {
    100% {
      right: 2rem;
      opacity: 100%;
    }
  }
}
