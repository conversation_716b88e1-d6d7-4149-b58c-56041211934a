import { ROUTES } from '@constants/routes';
import { InsightPage } from '@routes/summary/insights/utils/constants';
import { getValidPage, getInsightPageOptions, isInsightLayoutPage } from '@routes/summary/insights/utils/helpers';
import { InsightDashboardOption } from '@routes/summary/insights/utils/sidebar';
import { generateUrl } from '@routes/util';
import { useHistory } from 'react-router-dom';
import { InsightDashboard } from '@g17eco/types/insight-custom-dashboard';
import { MainDownloadCode } from '../config/app-config';
import { useCustomDashboards } from './useCustomDashboards';

interface Props {
  initiativeId: string;
  portfolioId: string;
  summaryPage: InsightPage;
  dashboardId?: string;
  dashboards: Pick<InsightDashboard, '_id' | 'title'>[];
  mainDownloadCode: MainDownloadCode | undefined;
}

export const usePTCompanyCustomDashboards = ({
  initiativeId,
  portfolioId,
  summaryPage: summaryPageParam = InsightPage.Overview,
  dashboardId = '',
  dashboards,
  mainDownloadCode,
}: Props) => {
  const history = useHistory();
  const summaryPage = isInsightLayoutPage(summaryPageParam) ? summaryPageParam : undefined;

  const dashboardOptions: InsightDashboardOption[] = dashboards.map((dashboard) => ({
    value: dashboard._id,
    label: dashboard.title,
    isCustom: true,
    disabled: false,
    isSharedByParent: false,
  }));

  const handleNavigateCustom = (dashboardId: string) => {
    const url = generateUrl(ROUTES.PORTFOLIO_TRACKER_COMPANY_DASHBOARD, {
      portfolioId,
      companyId: initiativeId,
      dashboardId,
    });
    history.push(url);
  };

  const { dashboardsToShow, showMoreDashboardsBtn } = useCustomDashboards({
    initiativeId: portfolioId,
    dashboardId,
    dashboardOptions,
    handleNavigate: handleNavigateCustom,
  });

  const handleNavigate = (page: string) => {
    const url = generateUrl(ROUTES.PORTFOLIO_TRACKER_COMPANY, {
      portfolioId,
      companyId: initiativeId,
      summaryPage: page,
    });
    history.push(url);
  };

  const handleClickOption = (item: InsightDashboardOption) => {
    if (item.disabled) {
      return;
    }
    return item.isCustom ? handleNavigateCustom(item.value) : handleNavigate(item.value);
  };

  const insightPageOptions: InsightDashboardOption[] = getInsightPageOptions(mainDownloadCode);

  return {
    currentPage: dashboardId || getValidPage(summaryPage),
    options: [...insightPageOptions, ...dashboardsToShow],
    handleNavigate,
    handleClickOption,
    showMoreDashboardsBtn,
  };
};
