import {
  useGetDashboardPreferencesQuery,
  useUpdateDashboardPreferencesMutation,
  useCreateInsightDashboardMutation,
} from '@api/insight-dashboards';
import { skipToken } from '@reduxjs/toolkit/query';
import { useEffect, useState } from 'react';
import { getVisibleDashboards } from '@utils/custom-dashboard';
import { useToggle } from './useToggle';
import { Button } from 'reactstrap';
import { InsightDashboardOption } from '@routes/summary/insights/utils/sidebar';
import { DashboardPreference } from '@g17eco/types/insight-custom-dashboard';
import { DEFAULT_FILTERS } from '@routes/custom-dashboard/dashboard-settings/utils';

interface Props {
  initiativeId: string;
  dashboardId: string;
  dashboardOptions: InsightDashboardOption[];
  handleNavigate: (dashboardId: string, openSettings?: boolean) => void;
}

export const useCustomDashboards = ({ initiativeId, dashboardId, dashboardOptions, handleNavigate }: Props) => {
  const [isEditing, setIsEditing] = useState(false);
  const [openSettingsSidebar, toggleSettingsSidebar] = useToggle(false);
  const [isManageModalOpen, toggleManageModal] = useToggle(false);
  const [showAllDashboards, toggleViewAll, setShowAll] = useToggle(false);
  const [initialCheckDone, setInitialCheckDone] = useState(false);

  const { data: preferences = [], isFetching: isFetchingPreferences } = useGetDashboardPreferencesQuery(
    initiativeId || skipToken,
  );

  const { dashboardsToShow, hiddenDashboards, hasMoreDashboards } = getVisibleDashboards({
    dashboardOptions,
    preferences,
    showAllDashboards,
  });

  const [updatePreferences, updatePreferencesState] = useUpdateDashboardPreferencesMutation();

  const handleSavePreferences = async (newPreferences: DashboardPreference[]) => {
    await updatePreferences({ initiativeId, preferences: newPreferences }).unwrap();
  };

  useEffect(() => {
    if (!initialCheckDone && hiddenDashboards.some((dashboard) => dashboard.value === dashboardId)) {
      setShowAll(true);
      setInitialCheckDone(true);
    }
  }, [hiddenDashboards, dashboardId, initialCheckDone]);

  const [createInsightDashboard, createInsightDashboardState] = useCreateInsightDashboardMutation();

  const handleAddNew = async ({ title, createInstantly }: { title?: string; createInstantly?: boolean }) => {
    if (createInstantly) {
      const dashboard = await createInsightDashboard({
        initiativeId,
        title: title || 'Custom Dashboard',
        items: [],
        filters: DEFAULT_FILTERS,
      }).unwrap();
      handleNavigate(dashboard._id, true);
      return;
    }

    toggleSettingsSidebar();
  };

  const showMoreDashboardsBtn = hasMoreDashboards ? (
    <Button
      color='link-secondary'
      className='insights-dashboard__view-all-btn px-3 text-ThemeTextMedium'
      onClick={toggleViewAll}
    >
      {showAllDashboards ? 'Show less' : 'View all'}
      <i className={`fa ${showAllDashboards ? 'fa-angle-up' : 'fa-angle-down'} ml-1 text-sm`} />
    </Button>
  ) : null;

  return {
    isEditing,
    setIsEditing,
    openSettingsSidebar,
    toggleSettingsSidebar,
    handleAddNew,
    isFetchingPreferences,
    preferences,
    dashboardsToShow,
    showMoreDashboardsBtn,
    isManageModalOpen,
    toggleManageModal,
    toggleViewAll,
    handleSavePreferences,
    updatePreferencesState,
    createInsightDashboardState,
  };
};
