import { useEffect } from 'react';
import { useHistory } from 'react-router-dom';
import {
  UpdateParam,
  useCreateTemplateDashboardMutation,
  useDeleteInsightDashboardMutation,
  useDuplicateInsightDashboardMutation,
  useGetInsightDashboardTemplatesQuery,
  useGetInsightDashboardsByInitiativeQuery,
  useUpdateInsightDashboardMutation,
} from '@api/insight-dashboards';
import { ROUTES } from '@constants/routes';
import {
  DashboardTemplateType,
  GridDashboardItem,
  InsightDashboard,
  InsightDashboardType,
  OutputHookCustomDashboards,
} from '@g17eco/types/insight-custom-dashboard';
import { useAppSelector } from '@reducers/index';
import { skipToken } from '@reduxjs/toolkit/query';
import { InsightPage } from '@routes/summary/insights/utils/constants';
import { getValidPage, getInsightPageOptions, isInsightLayoutPage } from '@routes/summary/insights/utils/helpers';
import { InsightDashboardOption, TOOLTIP_MESSAGE } from '@routes/summary/insights/utils/sidebar';
import { generateUrl } from '@routes/util';
import { canManageCurrentLevel } from '@selectors/user';
import { FeaturePermissions } from '@services/permissions/FeaturePermissions';
import { getRootConfig } from '@selectors/globalData';
import { isIntegratedDashboard } from '@features/custom-dashboard/utils/dashboard-utils';
import { MainDownloadCode } from '../config/app-config';
import { useCustomDashboards } from './useCustomDashboards';
import { generateToast, toastFromError } from '@g17eco/molecules/toasts';
import { ManageDashboardButton } from '@routes/summary/insights/partials/sidebar/ManageDashboardButton';

const categorizeDashboards = ({
  canAccessCustomDashboards,
  initiativeId,
  dashboards,
}: {
  dashboards: InsightDashboard<GridDashboardItem>[] | undefined;
  initiativeId: string | undefined;
  canAccessCustomDashboards: boolean;
}) =>
  (dashboards || []).reduce<{
    custom: InsightDashboardOption[];
    integrated: InsightDashboardOption[];
  }>(
    (acc, dashboard) => {
      const baseProps = {
        value: dashboard._id,
        label: dashboard.title,
        isCustom: true,
        disabled: !canAccessCustomDashboards,
      };

      if (dashboard.type === InsightDashboardType.Custom) {
        acc.custom.push({
          ...baseProps,
          tooltip: !canAccessCustomDashboards ? TOOLTIP_MESSAGE.NOT_AVAILABLE_PLAN : undefined,
          isSharedByParent: initiativeId !== dashboard.initiativeId,
        });
      }

      if (isIntegratedDashboard(dashboard.type)) {
        acc.integrated.push({
          ...baseProps,
          canView: true,
          isSharedByParent: initiativeId !== dashboard.initiativeId,
        });
      }
      return acc;
    },
    { custom: [], integrated: [] },
  );

interface Props {
  initiativeId: string;
  dashboardId?: string;
  summaryPage?: InsightPage;
  surveyId?: string;
  isEditingDashboard?: boolean;
  mainDownloadCode: MainDownloadCode | undefined;
}

export const useCTCustomDashboards = ({
  initiativeId,
  dashboardId = '',
  summaryPage: summaryPageParam = InsightPage.Overview,
  surveyId = 'current',
  mainDownloadCode,
}: Props): OutputHookCustomDashboards => {
  const history = useHistory();
  const rootConfig = useAppSelector(getRootConfig);
  const canAccessCustomDashboards = FeaturePermissions.canAccessCustomDashboards(rootConfig);
  const canManage = useAppSelector(canManageCurrentLevel);

  const {
    data: dashboards = [],
    isFetching: isFetchingDashboards,
    error: dashboardsError,
  } = useGetInsightDashboardsByInitiativeQuery(initiativeId || skipToken);

  const insightPageOptions: InsightDashboardOption[] = getInsightPageOptions(mainDownloadCode);

  const { custom: customDashboardOptions, integrated: integratedDashboardOptions } = categorizeDashboards({
    dashboards,
    initiativeId,
    canAccessCustomDashboards,
  });

  const dashboardOptions = [...integratedDashboardOptions, ...customDashboardOptions];

  const handleNavigateCustom = (dashboardId: string, openSettings?: boolean) => {
    const url = generateUrl(ROUTES.CUSTOM_DASHBOARD, { initiativeId, surveyId, dashboardId });
    const finalUrl = openSettings ? `${url}?openSettings=true` : url;
    history.push(finalUrl);
  };

  const {
    isEditing,
    setIsEditing,
    openSettingsSidebar,
    toggleSettingsSidebar,
    handleAddNew,
    isFetchingPreferences,
    dashboardsToShow,
    showMoreDashboardsBtn,
    preferences,
    isManageModalOpen,
    toggleManageModal,
    toggleViewAll,
    handleSavePreferences,
    updatePreferencesState,
    createInsightDashboardState,
  } = useCustomDashboards({
    initiativeId,
    dashboardId,
    dashboardOptions,
    handleNavigate: handleNavigateCustom,
  });

  const {
    data: templates = [],
    isFetching: isFetchingTemplates,
    error: templatesError,
  } = useGetInsightDashboardTemplatesQuery(initiativeId && canManage ? initiativeId : skipToken);
  const [createTemplateDashboard, createTemplateState] = useCreateTemplateDashboardMutation();
  const [updateDashboard, updateDashboardState] = useUpdateInsightDashboardMutation();
  const [duplicateDashboard, duplicateDashboardState] = useDuplicateInsightDashboardMutation();
  const [deleteDashboard, deleteDashboardState] = useDeleteInsightDashboardMutation();

  const isDataLoading = isFetchingDashboards || isFetchingPreferences || isFetchingTemplates;

  const isLoading =
    isDataLoading ||
    updatePreferencesState.isLoading ||
    createTemplateState.isLoading ||
    updateDashboardState.isLoading ||
    duplicateDashboardState.isLoading ||
    deleteDashboardState.isLoading ||
    createInsightDashboardState.isLoading;

  const error =
    dashboardsError ||
    templatesError ||
    updatePreferencesState.error ||
    createTemplateState.error ||
    updateDashboardState.error ||
    duplicateDashboardState.error ||
    deleteDashboardState.error ||
    createInsightDashboardState.error;

  useEffect(() => {
    if (error) {
      toastFromError({ error, title: 'Something went wrong' });
    }
  }, [error]);

  const handleUpdateDashboard = async (params: UpdateParam) => {
    await updateDashboard(params).unwrap();
  };

  const handleDuplicateDashboard = async (targetDashboardId: string) => {
    const response = await duplicateDashboard({ dashboardId: targetDashboardId, initiativeId }).unwrap();
    generateToast({
      color: 'success',
      title: (
        <>
          <i className='fal fa-copy text-sm mr-2'></i>Dashboard duplicated
        </>
      ),
      message: 'Dashboard has been duplicated successfully.',
    });
    return response;
  };

  const handleDeleteDashboard = async (targetDashboardId: string) => {
    return deleteDashboard({ dashboardId: targetDashboardId, initiativeId })
      .then(() => {
        generateToast({
          color: 'success',
          title: (
            <>
              <i className='fal fa-trash text-sm mr-2'></i>Dashboard deleted
            </>
          ),
          message: 'Dashboard has been deleted successfully.',
        });
        history.push(generateUrl(ROUTES.SUMMARY, { dashboardId: targetDashboardId, initiativeId }));
      })
      .catch((error) => {
        toastFromError(error);
      })
      .finally(() => {
        setIsEditing(false);
      });
  };

  const manageDashboardBtn = canManage ? (
    <ManageDashboardButton
      canAccessCustomDashboards={canAccessCustomDashboards}
      isEditing={isEditing}
      toggleManageModal={toggleManageModal}
    />
  ) : null;

  const options = [...insightPageOptions, ...dashboardsToShow];

  const summaryPage = isInsightLayoutPage(summaryPageParam) ? summaryPageParam : undefined;
  const currentPage = dashboardId || getValidPage(summaryPage);

  const handleNavigate = (page: string) => {
    const url = generateUrl(ROUTES.SUMMARY, {
      initiativeId,
      surveyId,
      summaryPage: page !== InsightPage.Overview ? page : undefined,
    });
    history.push(url);
  };

  const handleClickOption = async (item: InsightDashboardOption) => {
    if (item.disabled && !item.canView) {
      return;
    }
    return item.isCustom ? handleNavigateCustom(item.value) : handleNavigate(item.value);
  };

  const handleCreateTemplate = async (templateType: DashboardTemplateType) => {
    if (!initiativeId) {
      return;
    }
    const dashboard = await createTemplateDashboard({
      initiativeId,
      templateType,
    }).unwrap();
    return handleNavigateCustom(dashboard._id);
  };

  return {
    canManage,
    currentPage,
    options,
    isEditing,
    setIsEditing,
    handleNavigateCustom,
    handleNavigate,
    handleClickOption,
    dashboards,
    dashboardOptions,
    preferences,
    templates,
    handleCreateTemplate,
    manageDashboardBtn,
    isManageModalOpen,
    toggleManageModal,
    toggleViewAll,
    openSettingsSidebar,
    toggleSettingsSidebar,
    handleAddNew,
    showMoreDashboardsBtn,
    handleUpdateDashboard,
    handleDuplicateDashboard,
    handleDeleteDashboard,
    handleSavePreferences,
    isDataLoading,
    isUpdatingPreferences: updatePreferencesState.isLoading,
    isLoading,
    error,
  };
};
