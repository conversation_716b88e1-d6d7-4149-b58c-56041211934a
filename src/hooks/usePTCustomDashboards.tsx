import { ROUTES } from '@constants/routes';
import { useAppSelector } from '@reducers/index';
import { InsightDashboardOption } from '@routes/summary/insights/utils/sidebar';
import { generateUrl } from '@routes/util';
import { isUserManagerByPortfolioId } from '@selectors/user';
import {
  useDeletePortfolioInsightDashboardMutation,
  useDuplicatePortfolioInsightDashboardMutation,
  useGetInsightDashboardsByPortfolioQuery,
  useUpdatePortfolioInsightDashboardMutation,
} from '@api/portfolio-insight-dashboards';
import { generateErrorToast, generateToast, toastFromError } from '@g17eco/molecules/toasts';
import { useCustomDashboards } from './useCustomDashboards';
import { useEffect } from 'react';
import { useHistory } from 'react-router-dom';
import { ManageDashboardButton } from '@routes/summary/insights/partials/sidebar/ManageDashboardButton';
import { UpdateParam } from '@api/insight-dashboards';

interface Props {
  portfolioId: string;
  dashboardId?: string;
}

export const usePTCustomDashboards = ({ portfolioId, dashboardId = '' }: Props) => {
  const history = useHistory();
  const isManager = useAppSelector((state) => isUserManagerByPortfolioId(state, portfolioId));

  const {
    data: dashboards = [],
    isFetching: isFetchingDashboards,
    error: dashboardsError,
  } = useGetInsightDashboardsByPortfolioQuery(portfolioId, {
    skip: !isManager,
  });

  const dashboardOptions: InsightDashboardOption[] = dashboards.map((dashboard) => ({
    value: dashboard._id,
    label: dashboard.title,
    isCustom: true,
    isSharedByParent: false,
  }));

  const handleNavigateCustom = (dashboardId: string, openSettings?: boolean) => {
    const url = generateUrl(ROUTES.PORTFOLIO_TRACKER_INSIGHT_DASHBOARDS, { portfolioId, dashboardId });
    const finalUrl = openSettings ? `${url}?openSettings=true` : url;
    history.push(finalUrl);
  };

  const {
    isEditing,
    setIsEditing,
    openSettingsSidebar,
    toggleSettingsSidebar,
    handleAddNew,
    isFetchingPreferences,
    dashboardsToShow,
    showMoreDashboardsBtn,
    preferences,
    isManageModalOpen,
    toggleManageModal,
    toggleViewAll,
    handleSavePreferences,
    updatePreferencesState,
    createInsightDashboardState,
  } = useCustomDashboards({
    initiativeId: portfolioId,
    dashboardId,
    dashboardOptions,
    handleNavigate: handleNavigateCustom,
  });

  const [updateDashboard, updateDashboardState] = useUpdatePortfolioInsightDashboardMutation();
  const [deleteDashboard, deleteDashboardState] = useDeletePortfolioInsightDashboardMutation();
  const [duplicateDashboard, duplicateDashboardState] = useDuplicatePortfolioInsightDashboardMutation();

  const isDataLoading = isFetchingDashboards || isFetchingPreferences;

  const isLoading =
    isDataLoading ||
    updatePreferencesState.isLoading ||
    updateDashboardState.isLoading ||
    duplicateDashboardState.isLoading ||
    deleteDashboardState.isLoading ||
    createInsightDashboardState.isLoading;

  const error =
    dashboardsError ||
    updatePreferencesState.error ||
    updateDashboardState.error ||
    duplicateDashboardState.error ||
    deleteDashboardState.error ||
    createInsightDashboardState.error;

  useEffect(() => {
    if (error) {
      toastFromError({ error, title: 'Something went wrong' });
    }
  }, [error]);

  const handleUpdateDashboard = async (params: UpdateParam) => {
    await updateDashboard(params).unwrap();
  };

  const handleDeleteDashboard = async (targetDashboardId: string) => {
    return deleteDashboard({ dashboardId: targetDashboardId, initiativeId: portfolioId })
      .unwrap()
      .then(() => {
        generateToast({
          color: 'success',
          title: (
            <>
              <i className='fal fa-trash text-sm mr-2'></i>Dashboard deleted
            </>
          ),
          message: 'Dashboard has been deleted successfully.',
        });
        history.push(generateUrl(ROUTES.PORTFOLIO_TRACKER_INSIGHTS, { portfolioId }));
      })
      .catch((error) => {
        generateErrorToast(error);
      })
      .finally(() => {
        setIsEditing(false);
      });
  };

  const handleDuplicateDashboard = async (targetDashboardId: string) => {
    return duplicateDashboard({ dashboardId: targetDashboardId, initiativeId: portfolioId }).unwrap();
  };

  const manageDashboardBtn = (
    <ManageDashboardButton
      canAccessCustomDashboards={isManager}
      isEditing={isEditing}
      toggleManageModal={toggleManageModal}
    />
  );

  const options: InsightDashboardOption[] = [
    {
      label: 'SDG Insights',
      value: '',
    },
    ...dashboardsToShow,
  ];

  const handleNavigate = () => {
    history.push(generateUrl(ROUTES.PORTFOLIO_TRACKER_INSIGHTS, { portfolioId }));
  };

  const handleClickOption = (item: InsightDashboardOption) => {
    if (item.disabled) {
      return;
    }
    if (isManager && item.isCustom) {
      return handleNavigateCustom(item.value);
    }
    return handleNavigate();
  };

  return {
    currentPage: dashboardId,
    options,
    isEditing,
    setIsEditing,
    handleNavigateCustom,
    handleNavigate,
    handleClickOption,
    dashboards,
    dashboardOptions,
    showMoreDashboardsBtn,
    preferences,
    manageDashboardBtn,
    isManageModalOpen,
    toggleManageModal,
    toggleViewAll,
    openSettingsSidebar,
    toggleSettingsSidebar,
    handleAddNew,
    handleUpdateDashboard,
    handleDuplicateDashboard,
    handleDeleteDashboard,
    handleSavePreferences,
    isDataLoading,
    isUpdatingPreferences: updatePreferencesState.isLoading,
    isLoading,
    error,
  };
};
