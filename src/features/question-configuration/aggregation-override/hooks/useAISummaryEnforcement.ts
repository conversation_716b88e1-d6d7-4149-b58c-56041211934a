import { useCallback } from 'react';
import { FeaturePermissions } from '@services/permissions/FeaturePermissions';
import { useAppSelector } from '@reducers/index';
import { getRootConfig } from '@selectors/globalData';
import { AiProcessingValue } from '@g17eco/types/ai';
import {
  InitiativeUniversalTracker,
} from '@g17eco/types/initiativeUniversalTracker';
import { AI_FEATURE_MESSAGE } from '../utils';

interface AISummaryInfo {
  enable: boolean;
  reason: string;
}

export const useAISummaryEnforcement = (initiativeUtr: InitiativeUniversalTracker | undefined) => {
  const rootConfig = useAppSelector(getRootConfig);
  const canUseAISummary = FeaturePermissions.canAccessAITextSummarization(rootConfig);

  const getAISummaryInfo = useCallback((): AISummaryInfo => {
    // Check if the feature is enabled at the initiative level
    if (!canUseAISummary) {
      return {
        enable: false,
        reason: AI_FEATURE_MESSAGE.NOT_ENABLED,
      };
    }

    // Check if AI processing is disabled for this specific metric
    if (initiativeUtr?.aiProcessing === AiProcessingValue.Exclude) {
      return {
        enable: false,
        reason: AI_FEATURE_MESSAGE.IS_EXCLUDED,
      };
    }

    return { enable: true, reason: '' };
  }, [canUseAISummary, initiativeUtr]);

  return {
    getAISummaryInfo,
    canUseAISummary,
  };
};
