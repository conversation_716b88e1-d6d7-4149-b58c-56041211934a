import { describe, it, expect, vi, beforeEach } from 'vitest';
import { renderHook } from '@testing-library/react';
import { AiProcessingValue } from '@g17eco/types/ai';
import { useAISummaryEnforcement } from './useAISummaryEnforcement';
import { FeaturePermissions } from '@services/permissions/FeaturePermissions';
import { useAppSelector } from '@reducers/index';
import { createInitiativeUtr } from '@fixtures/initiative-universal-trackers-fixture';
import { AI_FEATURE_MESSAGE } from '../utils';

// Mock FeaturePermissions
vi.mock('@services/permissions/FeaturePermissions', () => ({
  FeaturePermissions: {
    canAccessAITextSummarization: vi.fn(),
  },
}));

// Mock Redux selectors
vi.mock('@selectors/globalData', () => ({
  getRootConfig: vi.fn(),
}));

// Mock useAppSelector
vi.mock('@reducers/index', () => ({
  useAppSelector: vi.fn(),
}));

const mockCanAccessAITextSummarization = vi.mocked(FeaturePermissions.canAccessAITextSummarization);
const mockUseAppSelector = vi.mocked(useAppSelector);

describe('useAISummaryEnforcement', () => {
  const mockRootConfig = { features: [] };

  beforeEach(() => {
    vi.clearAllMocks();
    mockUseAppSelector.mockReturnValue(mockRootConfig);
    mockCanAccessAITextSummarization.mockReturnValue(true);
  });

  const mockInitiativeUtr = createInitiativeUtr({
    _id: 'test-id',
    initiativeId: 'initiative-id',
    universalTrackerId: 'utr-id',
    aiProcessing: AiProcessingValue.Include,
  });

  describe('getAISummaryInfo', () => {
    it('should return enabled when feature is available and AI processing is include', () => {
      mockCanAccessAITextSummarization.mockReturnValue(true);

      const { result } = renderHook(() => useAISummaryEnforcement(mockInitiativeUtr));

      const info = result.current.getAISummaryInfo();
      expect(info.enable).toBe(true);
      expect(info.reason).toBe('');
    });

    it('should return disabled when feature is not available globally', () => {
      mockCanAccessAITextSummarization.mockReturnValue(false);

      const { result } = renderHook(() => useAISummaryEnforcement(mockInitiativeUtr));

      const info = result.current.getAISummaryInfo();
      expect(info.enable).toBe(false);
      expect(info.reason).toBe(AI_FEATURE_MESSAGE.NOT_ENABLED);
    });

    it('should return disabled when feature is available but AI processing is excluded for the metric', () => {
      mockCanAccessAITextSummarization.mockReturnValue(true);

      const { result } = renderHook(() =>
        useAISummaryEnforcement({ ...mockInitiativeUtr, aiProcessing: AiProcessingValue.Exclude }),
      );

      const info = result.current.getAISummaryInfo();
      expect(info.enable).toBe(false);
      expect(info.reason).toBe('AI processing is excluded for this metric');
    });

    it('should return enabled when initiativeUtr is undefined but feature is available', () => {
      mockCanAccessAITextSummarization.mockReturnValue(true);

      const { result } = renderHook(() => useAISummaryEnforcement(undefined));

      const info = result.current.getAISummaryInfo();
      expect(info.enable).toBe(true);
      expect(info.reason).toBe('');
    });
  });

  describe('canUseAISummary', () => {
    it('should return the result from FeaturePermissions.canAccessAITextSummarization', () => {
      mockCanAccessAITextSummarization.mockReturnValue(true);

      const { result } = renderHook(() => useAISummaryEnforcement(undefined));

      expect(result.current.canUseAISummary).toBe(true);
      expect(mockCanAccessAITextSummarization).toHaveBeenCalledWith(mockRootConfig);
    });

    it('should return false when feature is not available', () => {
      mockCanAccessAITextSummarization.mockReturnValue(false);

      const { result } = renderHook(() => useAISummaryEnforcement(undefined));

      expect(result.current.canUseAISummary).toBe(false);
    });
  });
});
