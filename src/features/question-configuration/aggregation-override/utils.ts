import {
  InitiativeUniversalTracker,
  ColumnValueAggregationOverride,
  ValueAggregationOverride,
  AggregationOverrideData,
  ColumnAggregationConfigOverride,
} from '@g17eco/types/initiativeUniversalTracker';
import { BaseAggregationConfig } from '@g17eco/types/universalTracker';
import {
  AggregationMode,
  UtrValueType,
  ColumnAggregationCompatibility,
  ColumnType,
  ColumnValueAggregation,
  columnAggregationTypes,
  getCompatibleAggregations,
  ValueAggregation,
} from '@g17eco/core';
import { aggregationLabels } from '@utils/universalTracker';
import { Option } from '@g17eco/molecules/select/SelectFactory';
import { BulkActionUtr } from '@g17eco/types/survey-question-list';
import { CSSProperties } from 'react';

export const AI_FEATURE_MESSAGE = {
  NOT_ENABLED: 'This feature will only work if \'AI summarisation\' is enabled in account settings.',
  IS_EXCLUDED: 'AI processing is excluded for this metric',
}

export const getInitialAggregationOverrideData = ({
  initiativeUtrMap,
  utr,
}: {
  initiativeUtrMap: Map<string, InitiativeUniversalTracker>;
  utr: BulkActionUtr;
}) => {
  const defaultConfig: AggregationOverrideData = {
    aggregationConfig: {
      modes: {
        children: {
          valueAggregation: 'default',
        },
        combined: {
          valueAggregation: 'default',
        },
      },
    },
    table: undefined,
  };

  if (!utr._id || initiativeUtrMap.size === 0) {
    return defaultConfig;
  }

  const existingOverrides = initiativeUtrMap.get(utr._id);
  const result: AggregationOverrideData = {
    aggregationConfig: {
      modes: {
        children: {
          valueAggregation: existingOverrides?.aggregationConfig?.modes?.children?.valueAggregation || 'default',
        },
        combined: {
          valueAggregation: existingOverrides?.aggregationConfig?.modes?.combined?.valueAggregation || 'default',
        },
      },
    },
    table: undefined,
  };

  if (utr.valueType === UtrValueType.Table) {
    const existingColumnsOverride = existingOverrides?.valueValidation?.table?.columns || [];
    const utrColumns = utr.valueValidation?.table?.columns || [];

    result.table = {
      columns: utrColumns.map((column) => {
        const existingColumn = existingColumnsOverride.find((ec) => ec.code === column.code);
        const childrenConfig = existingColumn?.aggregationConfig?.modes?.children;
        const combinedConfig = existingColumn?.aggregationConfig?.modes?.combined;
        return (
          existingColumn || {
            code: column.code,
            name: column.name,
            type: column.type,
            aggregationConfig: {
              modes: {
                children: { valueAggregation: childrenConfig?.valueAggregation || 'default', weightFormula: childrenConfig?.weightFormula },
                combined: { valueAggregation: combinedConfig?.valueAggregation || 'default', weightFormula: combinedConfig?.weightFormula },
              },
            },
          }
        );
      }),
    };
  }

  return result;
};

export const getAggregationOptions = ({
  valueType,
  mode,
  aiSummary,
}: {
  valueType: string | undefined;
  mode: AggregationMode;
  aiSummary: { enable: boolean; reason: string };
}) => {
  const options: Option<ValueAggregationOverride>[] = [
    {
      label: aggregationLabels['default'],
      value: 'default',
    },
  ];

  if (!valueType) {
    return options;
  }

  const compatibleAggregations = getCompatibleAggregations(valueType as UtrValueType, mode);

  // Add compatible aggregation options
  if (compatibleAggregations.length > 0) {
    const aggregationOptions = compatibleAggregations.map((option) => {
      if (option === ValueAggregation.TextAISummaryAggregator && valueType === UtrValueType.Text) {
        return {
          label: aggregationLabels[option] ?? option,
          value: option,
          isDisabled: !aiSummary.enable,
          tooltip: aiSummary.enable ? undefined : aiSummary.reason,
        };
      }

      return {
        label: aggregationLabels[option] ?? option,
        value: option,
      };
    });
    options.push(...aggregationOptions);
  }

  return options;
};

// Get compatible aggregation options for this column type
export const getColumnAggregationOptions = ({
  columnType,
  aiSummary,
}: {
  columnType: ColumnType;
  aiSummary: { enable: boolean; reason: string };
}): Option<ColumnValueAggregationOverride>[] => {
  const compatibility = ColumnAggregationCompatibility[columnType];

  if (!compatibility) {
    return [{ label: 'Use default settings', value: 'default' }];
  }

  const options: Option<ColumnValueAggregationOverride>[] = [{ label: 'Use default settings', value: 'default' }];

  // Add compatible aggregations
  compatibility.compatible.forEach((aggregation) => {
    // Check if this is AI Summary and needs special handling
    if (aggregation === ColumnValueAggregation.ColumnAISummaryAggregator && columnType === ColumnType.Text) {
      options.push({
        label: columnAggregationTypes[aggregation],
        value: aggregation,
        isDisabled: !aiSummary.enable,
        tooltip: aiSummary.enable ? undefined : aiSummary.reason,
      });
    } else {
      options.push({
        label: columnAggregationTypes[aggregation],
        value: aggregation,
      });
    }
  });

  return options;
};

/**
 * Badge styling constants
 */
export const BADGE_STYLES = {
  default: {
    fontSize: '0.55rem',
    fontWeight: '500',
    padding: '0.15rem 0.3rem',
  } as CSSProperties,
};

/**
 * Mode display prefixes
 */
export const MODE_PREFIXES = {
  children: 'Ch:',
  combined: 'Co:',
} as const;

/**
 * Badge color mappings
 */
export const BADGE_COLORS = {
  children: 'info',
  combined: 'success',
} as const;

export const hasAnyAggregation = (aggregationConfig: ColumnAggregationConfigOverride): boolean => {
  const configuredModes = Object.keys(aggregationConfig.modes || {});
  return configuredModes.length > 0;
};

export const isWeightedAverageAggregation = (aggregation?: ColumnValueAggregation | string): boolean => {
  return aggregation === ColumnValueAggregation.ColumnWeightedAverageAggregator;
};

export const getAggregationTooltip = (
  aggregation: ColumnValueAggregation | string,
  mode?: 'children' | 'combined' | 'default',
): string => {
  const prefix =
    mode === 'children' ? 'Children mode: ' : mode === 'combined' ? 'Combined mode: ' : 'Default aggregation: ';

  if (mode === 'default' && isWeightedAverageAggregation(aggregation)) {
    return 'Warning: Weighted average without mode-specific weight formula';
  }

  return prefix + aggregation;
};

export const getAggregationShortName = (aggregation: ColumnValueAggregation | string): string => {
  switch (aggregation) {
    case ColumnValueAggregation.ColumnSumAggregator:
      return 'SUM';
    case ColumnValueAggregation.ColumnAverageAggregator:
      return 'AVG';
    case ColumnValueAggregation.ColumnWeightedAverageAggregator:
      return 'W.AVG';
    case ColumnValueAggregation.ColumnMaxAggregator:
      return 'MAX';
    case ColumnValueAggregation.ColumnPostAggregationCalculation:
      return 'CALC';
    case ColumnValueAggregation.ColumnLatestAggregator:
      return 'LATEST';
    case ColumnValueAggregation.ColumnEmptyAggregator:
      return 'IGNORE';
    case ColumnValueAggregation.ColumnConcatenateAggregator:
      return 'CONCAT';
    case ColumnValueAggregation.ColumnAISummaryAggregator:
      return 'AI';
    default:
      return 'UNK';
  }
};

export const getStorageKey = (initiativeId: string) => {
  return `${initiativeId}-aggregation-warning`;
};

const AGGREGATORS_TO_SYNC = [
  ValueAggregation.ValueConcatenateAggregator,
  ValueAggregation.TextAISummaryAggregator,
  ColumnValueAggregation.ColumnConcatenateAggregator,
  ColumnValueAggregation.ColumnAISummaryAggregator,
];

const isAggregatorToSync = <T extends ValueAggregationOverride | ColumnValueAggregationOverride>(
  aggregation: T | undefined,
): aggregation is T => {
  return AGGREGATORS_TO_SYNC.includes(aggregation as ValueAggregation);
};

/**
 * Updates aggregation config with new valueAggregation for both children and combined modes.
 */
const updateValueAggregation = <T extends ValueAggregationOverride | ColumnValueAggregationOverride>(
  aggregationConfig: BaseAggregationConfig<T>,
  update: { childrenAgg: T; combinedAgg: T },
): BaseAggregationConfig<T> => {
  return {
    ...aggregationConfig,
    modes: {
      ...aggregationConfig.modes,
      children: {
        ...aggregationConfig.modes?.children,
        valueAggregation: update.childrenAgg,
      },
      combined: {
        ...aggregationConfig.modes?.combined,
        valueAggregation: update.combinedAgg,
      },
    },
  };
};

/**
 * Determines which aggregator to use when syncing both modes.
 * Prefers the one that changed; defaults to combined mode if both need sync.
 */
const selectAggregatorToSync = <T extends ValueAggregationOverride | ColumnValueAggregationOverride>({
  newChildrenAgg,
  newCombinedAgg,
  prevChildrenAgg,
  prevCombinedAgg,
}: {
  newChildrenAgg: T | undefined;
  newCombinedAgg: T | undefined;
  prevChildrenAgg: T | undefined;
  prevCombinedAgg: T | undefined;
}): T => {
  // Prefer the one that changed
  if (newCombinedAgg !== prevCombinedAgg && isAggregatorToSync(newCombinedAgg)) {
    return newCombinedAgg;
  }
  if (newChildrenAgg !== prevChildrenAgg && isAggregatorToSync(newChildrenAgg)) {
    return newChildrenAgg;
  }
  // Fallback: prefer combined mode
  return (isAggregatorToSync(newCombinedAgg) ? newCombinedAgg : newChildrenAgg) as T;
};

/**
 * Generic function to enforce consistency for aggregation configs.
 *
 * Rules:
 * 1. Leaving sync mode:
 *    If either children or combined mode is set to AI summary or concatenate, both must use AI summary or concatenate
 * 2. Leaving sync mode: 
 *    If changing from AI/concat to non-AI/concat in either mode, reset other remaining mode to 'default'
 * 3. No sync needed:
 *    If changing between non-AI/concat aggregators, keep new config unchanged
 */
export const enforceAggregationConsistency = <T extends ValueAggregationOverride | ColumnValueAggregationOverride>(
  newConfig: BaseAggregationConfig<T>,
  previousConfig: BaseAggregationConfig<T> | undefined,
): BaseAggregationConfig<T> => {
  const newChildrenAgg = newConfig.modes?.children?.valueAggregation;
  const newCombinedAgg = newConfig.modes?.combined?.valueAggregation;

  const childrenHasAggToSync = isAggregatorToSync(newChildrenAgg);
  const combinedHasAggToSync = isAggregatorToSync(newCombinedAgg);

  const prevChildrenAgg = previousConfig?.modes?.children?.valueAggregation;
  const prevCombinedAgg = previousConfig?.modes?.combined?.valueAggregation;

  // --- RULE 2: Leaving sync mode
  if (previousConfig?.modes) {
    const prevChildrenHasAggToSync = isAggregatorToSync(prevChildrenAgg);
    const prevCombineHasAggToSync = isAggregatorToSync(prevCombinedAgg);

    const isChildrenLeavingSyncMode = prevChildrenHasAggToSync && !childrenHasAggToSync;
    const isCombinedLeavingSyncMode = prevCombineHasAggToSync && !combinedHasAggToSync;

    // 2.1 children changes from AI/concat → non-AI/concat
    if (isChildrenLeavingSyncMode) {
      return updateValueAggregation(newConfig, {
        childrenAgg: (newChildrenAgg || 'default') as T,
        combinedAgg: 'default' as T,
      });
    }

    // 2.2 combined changes from AI/concat → non-AI/concat
    if (isCombinedLeavingSyncMode) {
      return updateValueAggregation(newConfig, {
        childrenAgg: 'default' as T,
        combinedAgg: (newCombinedAgg || 'default') as T,
      });
    }
  }

  // --- RULE 1: Entering sync mode
  if (childrenHasAggToSync || combinedHasAggToSync) {
    const aggToSync = selectAggregatorToSync({ newChildrenAgg, newCombinedAgg, prevChildrenAgg, prevCombinedAgg });
    return updateValueAggregation(newConfig, { childrenAgg: aggToSync, combinedAgg: aggToSync });
  }

  // --- RULE 3: No sync needed
  return newConfig;
};
