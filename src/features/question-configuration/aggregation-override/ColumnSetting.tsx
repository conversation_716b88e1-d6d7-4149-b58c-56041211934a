import React, { useState } from 'react';
import { Button, Col, FormGroup, Label, Row } from 'reactstrap';
import { SelectFactory, SelectTypes, Option } from '@g17eco/molecules/select/SelectFactory';
import {
  ColumnValueAggregationOverride,
  TableColumnOverride,
  InitiativeUniversalTracker,
  ColumnAggregationConfigOverride,
} from '@g17eco/types/initiativeUniversalTracker';
import { enforceAggregationConsistency, getColumnAggregationOptions, isWeightedAverageAggregation } from './utils';
import { AggregationModeConfig as AggregationModeConfigType, TableColumn } from '@g17eco/types/universalTracker';
import { AggregationMode, ColumnValueAggregation, ColumnType } from '@g17eco/core';
import { BasicAlert } from '@g17eco/molecules/alert';
import { WeightFormulaInput } from './WeightFormulaInput';
import { useAISummaryEnforcement } from './hooks/useAISummaryEnforcement';

interface AggregationModeConfigProps {
  title: string;
  description: string;
  mode: AggregationMode;
  config: AggregationModeConfigType<ColumnValueAggregationOverride> | undefined;
  aggregationOptions: Option<ColumnValueAggregationOverride>[];
  onAggregationChange: (option: Option<ColumnValueAggregationOverride> | null, mode: AggregationMode) => void;
  onWeightFormulaChange: (value: string, mode: AggregationMode) => void;
  availableColumns: TableColumn[];
  currentColumnCode: string;
}

const AggregationModeConfig = ({
  title,
  description,
  mode,
  config,
  aggregationOptions,
  onAggregationChange,
  onWeightFormulaChange,
  availableColumns,
  currentColumnCode,
}: AggregationModeConfigProps) => {
  return (
    <Col cols={6} className='border rounded p-3'>
      <Label style={{ fontWeight: 500 }} className='mb-0'>
        {title}
      </Label>
      <div className='text-muted small mb-1'>{description}</div>

      <FormGroup>
        <Label>Aggregation Method</Label>
        <SelectFactory
          selectType={SelectTypes.SingleSelect}
          options={aggregationOptions}
          value={aggregationOptions.find((opt) => opt.value === config?.valueAggregation) || null}
          onChange={(option) => onAggregationChange(option, mode)}
          isMenuPortalTargetBody
          menuPlacement='auto'
        />
      </FormGroup>

      {config?.valueAggregation === ColumnValueAggregation.ColumnPostAggregationCalculation ? (
        <BasicAlert type='warning'>
          <strong>Warning:</strong> Post-aggregation calculation is selected. Universal trackers with post-aggregation
          calculation require a calculation formula to ensure proper functionality.
        </BasicAlert>
      ) : null}

      {isWeightedAverageAggregation(config?.valueAggregation) ? (
        <WeightFormulaInput
          mode={mode}
          value={config?.weightFormula || ''}
          onChange={(value) => onWeightFormulaChange(value, mode)}
          availableColumns={availableColumns}
          currentColumnCode={currentColumnCode}
        />
      ) : null}
    </Col>
  );
};

export type ModeConfig = {
  children?: AggregationModeConfigType<ColumnValueAggregationOverride>;
  combined?: AggregationModeConfigType<ColumnValueAggregationOverride>;
};

interface Props {
  column: TableColumnOverride;
  availableColumns: TableColumn[];
  onClose: () => void;
  handleChangeModeConfig: (params: { code: string; config: ModeConfig }) => void;
  initiativeUtr: InitiativeUniversalTracker | undefined;
}

export const ColumnSetting = ({ column, availableColumns, onClose, handleChangeModeConfig, initiativeUtr }: Props) => {
  const defaultConfig: ModeConfig = column.aggregationConfig?.modes || {};
  const [modeConfig, setModeConfig] = useState<ModeConfig>(defaultConfig);

  const { getAISummaryInfo } = useAISummaryEnforcement(initiativeUtr);

  const aggregationOptions = getColumnAggregationOptions({
    columnType: column.type,
    aiSummary: getAISummaryInfo(),
  });

  const handleSave = () => {
    handleChangeModeConfig({
      code: column.code,
      config: modeConfig,
    });
  };

  const handleCancel = () => {
    setModeConfig(defaultConfig);
    onClose();
  };

  const handleAggregationChange = (option: Option<ColumnValueAggregationOverride> | null, mode: AggregationMode) => {
    setModeConfig((prevModeConfig) => {
      const newModeConfig: ModeConfig = {
        ...prevModeConfig,
        [mode]: {
          ...prevModeConfig[mode],
          valueAggregation: option?.value || 'default',
        },
      };
      let newAggregationConfig: ColumnAggregationConfigOverride = {
        modes: newModeConfig,
      };

      // Enforce AI summary consistency for text columns
      if (column.type === ColumnType.Text) {
        newAggregationConfig = enforceAggregationConsistency(newAggregationConfig, {
          modes: prevModeConfig,
        });
      }

      return newAggregationConfig.modes || prevModeConfig;
    });
  };

  const handleWeightFormulaChange = (value: string, mode: AggregationMode) => {
    setModeConfig((prev) => ({
      ...prev,
      [mode]: {
        ...prev[mode],
        weightFormula: value,
      },
    }));
  };

  const commonProps = {
    aggregationOptions,
    onAggregationChange: handleAggregationChange,
    onWeightFormulaChange: handleWeightFormulaChange,
    availableColumns,
    currentColumnCode: column.code,
  };

  return (
    <div className='mb-3 p-3 bg-light rounded'>
      <div className='mb-3'>
        <p className='text-muted mb-0 small'>
          <i className='fa fa-info-circle me-1' />
          Configure mode-specific aggregation overrides. This allows different aggregation behavior for{' '}
          <strong>children mode</strong> (tree aggregations) vs <strong>combined mode</strong> (report aggregations).
        </p>
      </div>
      <div className='mb-2'>
        <Label className='text-muted small'>
          Column Code: <code>{column.code}</code> | Type: {column.type}
        </Label>
      </div>
      <Row className='mb-3 px-3 gap-3'>
        <AggregationModeConfig
          title='Children Mode'
          description='Used for tree aggregations up to parent entities'
          mode={AggregationMode.Children}
          config={modeConfig.children}
          {...commonProps}
        />
        <AggregationModeConfig
          title='Combined Mode'
          description='Used for combined reports (e.g., monthly to yearly)'
          mode={AggregationMode.Combined}
          config={modeConfig.combined}
          {...commonProps}
        />
      </Row>
      <div>
        <Button color='primary' onClick={handleSave}>
          Save Changes
        </Button>
        <Button color='transparent' onClick={handleCancel}>
          Cancel
        </Button>
      </div>
    </div>
  );
};
