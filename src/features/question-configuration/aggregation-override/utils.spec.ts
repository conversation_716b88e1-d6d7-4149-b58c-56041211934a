import { describe, it, expect } from 'vitest';
import {
  UtrValueType,
  AggregationMode,
  ColumnType,
  ColumnValueAggregation,
  ValueAggregation,
  columnAggregationTypes,
} from '@g17eco/core';
import { BulkActionUtr } from '@g17eco/types/survey-question-list';
import {
  InitiativeUniversalTracker,
  ValueAggregationOverride,
  ColumnValueAggregationOverride,
} from '@g17eco/types/initiativeUniversalTracker';
import type { BaseAggregationConfig } from '@g17eco/types/universalTracker';

import {
  getInitialAggregationOverrideData,
  getAggregationOptions,
  getColumnAggregationOptions,
  hasAnyAggregation,
  isWeightedAverageAggregation,
  getAggregationTooltip,
  getAggregationShortName,
  enforceAggregationConsistency,
} from './utils';
import { aggregationLabels } from '@utils/universalTracker';

describe('aggregation-override utils', () => {
  describe('getInitialAggregationOverrideData', () => {
    const mockUtr: BulkActionUtr = {
      _id: 'utr-123',
      valueType: UtrValueType.Number,
      code: 'test-utr',
      name: 'Test UTR',
    } as BulkActionUtr;

    it('should return existing overrides when found in map', () => {
      const existingOverrides: InitiativeUniversalTracker = {
        aggregationConfig: {
          modes: {
            children: { valueAggregation: ValueAggregation.ValueSumAggregator },
            combined: { valueAggregation: ValueAggregation.ValueAverageAggregator },
          },
        },
      } as InitiativeUniversalTracker;

      const initiativeUtrMap = new Map([['utr-123', existingOverrides]]);

      const result = getInitialAggregationOverrideData({
        initiativeUtrMap,
        utr: mockUtr,
      });

      expect(result.aggregationConfig?.modes?.children?.valueAggregation).toBe(ValueAggregation.ValueSumAggregator);
      expect(result.aggregationConfig?.modes?.combined?.valueAggregation).toBe(ValueAggregation.ValueAverageAggregator);
    });

    it('should handle table UTR with column overrides', () => {
      const tableUtr: BulkActionUtr = {
        ...mockUtr,
        valueType: UtrValueType.Table,
        valueValidation: {
          table: {
            columns: [
              { code: 'col1', name: 'Column 1', type: ColumnType.Number },
              { code: 'col2', name: 'Column 2', type: ColumnType.Text },
            ],
          },
        },
      } as BulkActionUtr;

      const existingOverrides: InitiativeUniversalTracker = {
        valueValidation: {
          table: {
            columns: [
              {
                code: 'col1',
                name: 'Column 1',
                type: ColumnType.Number,
                aggregationConfig: {
                  modes: {
                    children: { valueAggregation: ColumnValueAggregation.ColumnSumAggregator },
                  },
                },
              },
            ],
          },
        },
      } as InitiativeUniversalTracker;

      const initiativeUtrMap = new Map([['utr-123', existingOverrides]]);

      const result = getInitialAggregationOverrideData({
        initiativeUtrMap,
        utr: tableUtr,
      });

      expect(result.table?.columns).toHaveLength(2);
      expect(result.table?.columns[0].aggregationConfig?.modes?.children?.valueAggregation).toBe(
        ColumnValueAggregation.ColumnSumAggregator,
      );
      expect(result.table?.columns[1].aggregationConfig?.modes?.children?.valueAggregation).toBe('default');
    });
  });

  describe('getAggregationOptions', () => {
    it('should correct options for Number type in Children mode', () => {
      const options = getAggregationOptions({
        valueType: UtrValueType.Number,
        mode: AggregationMode.Children,
        aiSummary: { enable: false, reason: '' },
      });

      expect(options).toHaveLength(3); // default + 2 compatible
      expect(options[0].value).toBe('default');
      expect(options[1].value).toBe(ValueAggregation.ValueSumAggregator);
      expect(options[2].value).toBe(ValueAggregation.EmptyAggregator);
    });

    it('when type is not text, then AI summary should not have any effect', () => {
      // AI summary is enabled, but since valueType is not text, it should not have any effect
      let options = getAggregationOptions({
        valueType: UtrValueType.Number,
        mode: AggregationMode.Children,
        aiSummary: { enable: true, reason: '' },
      });

      expect(options).toHaveLength(3); // default + 2 compatible
      expect(options[0].value).toBe('default');
      expect(options[1].value).toBe(ValueAggregation.ValueSumAggregator);
      expect(options[2].value).toBe(ValueAggregation.EmptyAggregator);
      
      // AI summary is disabled, but since valueType is not text, it should not have any effect
      options = getAggregationOptions({
        valueType: UtrValueType.Number,
        mode: AggregationMode.Children,
        aiSummary: { enable: false, reason: 'Feature disabled' },
      });

      expect(options).toHaveLength(3); // default + 2 compatible
      expect(options[0].value).toBe('default');
      expect(options[1].value).toBe(ValueAggregation.ValueSumAggregator);
      expect(options[2].value).toBe(ValueAggregation.EmptyAggregator);
    });

    it('should correctly returns options for Text type in Combined mode when AI is disabled', () => {
      const options = getAggregationOptions({
        valueType: UtrValueType.Text,
        mode: AggregationMode.Combined,
        aiSummary: { enable: false, reason: 'Feature disabled' },
      });

      expect(options).toHaveLength(7); // default + 1 compatible (emptyAggregator)
      expect(options[0].value).toBe('default');
      expect(options[1].value).toBe(ValueAggregation.LatestAggregator);
      expect(options[2].value).toBe(ValueAggregation.EmptyAggregator);
      expect(options[3].value).toBe(ValueAggregation.ValueCountAggregator);
      expect(options[4].value).toBe(ValueAggregation.TextCountAggregator);
      expect(options[5].value).toBe(ValueAggregation.ValueConcatenateAggregator);
      expect(options[6]).toEqual({
        label: aggregationLabels[ValueAggregation.TextAISummaryAggregator],
        value: ValueAggregation.TextAISummaryAggregator,
        isDisabled: true,
        tooltip: 'Feature disabled',
      });
    });

    it('should correctly returns options for Text type in Combined mode when AI is enabled', () => {
      const options = getAggregationOptions({
        valueType: UtrValueType.Text,
        mode: AggregationMode.Combined,
        aiSummary: { enable: true, reason: '' },
      });

      expect(options).toHaveLength(7); // default + 1 compatible (emptyAggregator) + AI Summary
      expect(options[0].value).toBe('default');
      expect(options[1].value).toBe(ValueAggregation.LatestAggregator);
      expect(options[2].value).toBe(ValueAggregation.EmptyAggregator);
      expect(options[3].value).toBe(ValueAggregation.ValueCountAggregator);
      expect(options[4].value).toBe(ValueAggregation.TextCountAggregator);
      expect(options[5].value).toBe(ValueAggregation.ValueConcatenateAggregator);
      expect(options[6]).toEqual({
        label: aggregationLabels[ValueAggregation.TextAISummaryAggregator],
        value: ValueAggregation.TextAISummaryAggregator,
        isDisabled: false,
        tooltip: undefined,
      });
    });
  });

  describe('getColumnAggregationOptions', () => {
    it('number column type should not be affected by AI summary (false in this case)', () => {
      const options = getColumnAggregationOptions({
        columnType: ColumnType.Number,
        aiSummary: { enable: false, reason: 'Disabled reason' },
      });

      expect(options[0]).toEqual({
        label: 'Use default settings',
        value: 'default',
      });
      expect(options.length).toBeGreaterThan(1);
      expect(options[1].value).toBe(ColumnValueAggregation.ColumnSumAggregator);
      expect(options[2].value).toBe(ColumnValueAggregation.ColumnAverageAggregator);
      expect(options[3].value).toBe(ColumnValueAggregation.ColumnLatestAggregator);
      expect(options[4].value).toBe(ColumnValueAggregation.ColumnMaxAggregator);
      expect(options[5].value).toBe(ColumnValueAggregation.ColumnEmptyAggregator);
      expect(options[6].value).toBe(ColumnValueAggregation.ColumnPostAggregationCalculation);
      expect(options[7].value).toBe(ColumnValueAggregation.ColumnWeightedAverageAggregator);
    });

    it('number column type should not be affected by AI summary (true in this case)', () => {
      const options = getColumnAggregationOptions({
        columnType: ColumnType.Number,
        aiSummary: { enable: true, reason: '' },
      });

      expect(options[0]).toEqual({
        label: 'Use default settings',
        value: 'default',
      });
      expect(options.length).toBeGreaterThan(1);
      expect(options[1].value).toBe(ColumnValueAggregation.ColumnSumAggregator);
      expect(options[2].value).toBe(ColumnValueAggregation.ColumnAverageAggregator);
      expect(options[3].value).toBe(ColumnValueAggregation.ColumnLatestAggregator);
      expect(options[4].value).toBe(ColumnValueAggregation.ColumnMaxAggregator);
      expect(options[5].value).toBe(ColumnValueAggregation.ColumnEmptyAggregator);
      expect(options[6].value).toBe(ColumnValueAggregation.ColumnPostAggregationCalculation);
      expect(options[7].value).toBe(ColumnValueAggregation.ColumnWeightedAverageAggregator);
    });

    it('should return options for text column type when AI is disabled', () => {
      const options = getColumnAggregationOptions({
        columnType: ColumnType.Text,
        aiSummary: { enable: false, reason: 'Metric Ai Processing is disabled' },
      });

      expect(options[0]).toEqual({
        label: 'Use default settings',
        value: 'default',
      });
      expect(options.length).toBeGreaterThan(1);
      expect(options[1].value).toBe(ColumnValueAggregation.ColumnLatestAggregator);
      expect(options[2].value).toBe(ColumnValueAggregation.ColumnEmptyAggregator);
      expect(options[3].value).toBe(ColumnValueAggregation.ColumnConcatenateAggregator);
      expect(options[4]).toEqual({
        label: columnAggregationTypes[ColumnValueAggregation.ColumnAISummaryAggregator],
        value: ColumnValueAggregation.ColumnAISummaryAggregator,
        isDisabled: true,
        tooltip: 'Metric Ai Processing is disabled',
      });
    });

    it('should return options for text column type when AI is enabled', () => {
      const options = getColumnAggregationOptions({
        columnType: ColumnType.Text,
        aiSummary: { enable: true, reason: '' },
      });

      expect(options[0]).toEqual({
        label: 'Use default settings',
        value: 'default',
      });
      expect(options.length).toBeGreaterThan(1);
      expect(options[1].value).toBe(ColumnValueAggregation.ColumnLatestAggregator);
      expect(options[2].value).toBe(ColumnValueAggregation.ColumnEmptyAggregator);
      expect(options[3].value).toBe(ColumnValueAggregation.ColumnConcatenateAggregator);
      expect(options[4]).toEqual({
        label: columnAggregationTypes[ColumnValueAggregation.ColumnAISummaryAggregator],
        value: ColumnValueAggregation.ColumnAISummaryAggregator,
        isDisabled: false,
        tooltip: undefined,
      });
    });

    it('should return only default option for unsupported column type', () => {
      const options = getColumnAggregationOptions({
        columnType: 'unsupported' as ColumnType,
        aiSummary: { enable: false, reason: '' },
      });

      expect(options).toEqual([{ label: 'Use default settings', value: 'default' }]);
    });
  });

  describe('hasAnyAggregation', () => {
    it('should return true when aggregation config has modes', () => {
      const config = {
        modes: {
          children: { valueAggregation: ColumnValueAggregation.ColumnSumAggregator },
        },
      };

      expect(hasAnyAggregation(config)).toBe(true);
    });

    it('should return false when aggregation config has no modes', () => {
      const config = { modes: {} };
      expect(hasAnyAggregation(config)).toBe(false);
    });
  });

  describe('isWeightedAverageAggregation', () => {
    it('should return true for weighted average aggregation', () => {
      expect(isWeightedAverageAggregation('columnWeightedAverageAggregator')).toBe(true);
    });

    it('should return false for other aggregations', () => {
      expect(isWeightedAverageAggregation('columnSumAggregator')).toBe(false);
      expect(isWeightedAverageAggregation('other')).toBe(false);
      expect(isWeightedAverageAggregation(undefined)).toBe(false);
    });
  });

  describe('getAggregationTooltip', () => {
    it('should return children mode tooltip', () => {
      const tooltip = getAggregationTooltip('columnSumAggregator', 'children');
      expect(tooltip).toBe('Children mode: columnSumAggregator');
    });

    it('should return combined mode tooltip', () => {
      const tooltip = getAggregationTooltip('columnAverageAggregator', 'combined');
      expect(tooltip).toBe('Combined mode: columnAverageAggregator');
    });

    it('should return warning for default weighted average', () => {
      const tooltip = getAggregationTooltip('columnWeightedAverageAggregator', 'default');
      expect(tooltip).toBe('Warning: Weighted average without mode-specific weight formula');
    });

    it('should return default tooltip when no mode specified', () => {
      const tooltip = getAggregationTooltip('columnSumAggregator');
      expect(tooltip).toBe('Default aggregation: columnSumAggregator');
    });
  });

  describe('getAggregationShortName', () => {
    it('should return correct short names for aggregations', () => {
      expect(getAggregationShortName('columnSumAggregator')).toBe('SUM');
      expect(getAggregationShortName('columnAverageAggregator')).toBe('AVG');
      expect(getAggregationShortName('columnWeightedAverageAggregator')).toBe('W.AVG');
      expect(getAggregationShortName('columnMaxAggregator')).toBe('MAX');
      expect(getAggregationShortName('columnPostAggregationCalculation')).toBe('CALC');
      expect(getAggregationShortName('columnLatestAggregator')).toBe('LATEST');
      expect(getAggregationShortName('columnEmptyAggregator')).toBe('IGNORE');
      expect(getAggregationShortName('columnConcatenateAggregator')).toBe('CONCAT');
      expect(getAggregationShortName('columnAISummaryAggregator')).toBe('AI');
      expect(getAggregationShortName('unknown')).toBe('UNK');
    });
  });

  describe('enforceAggregationConsistency', () => {
    const buildValueConfig = ({
      children,
      combined,
    }: {
      children: ValueAggregationOverride | ColumnValueAggregationOverride;
      combined: ValueAggregationOverride | ColumnValueAggregationOverride;
    }): BaseAggregationConfig<ValueAggregationOverride | ColumnValueAggregationOverride> => ({
      modes: {
        children: { valueAggregation: children },
        combined: { valueAggregation: combined },
      },
    });

    describe('RULE 1: ValueAggregation - Entering AI/concat mode → sync both', () => {
      const aggsToSync = [ValueAggregation.TextAISummaryAggregator, ValueAggregation.ValueConcatenateAggregator];
      const otherAggs = [ValueAggregation.LatestAggregator, ValueAggregation.EmptyAggregator];

      aggsToSync.forEach((aggToSync) => {
        otherAggs.forEach((normalAgg) => {
          it(`should sync both when children=${String(aggToSync)} / combined=${String(normalAgg)}`, () => {
            const config = buildValueConfig({ children: aggToSync, combined: normalAgg });
            const result = enforceAggregationConsistency(config, undefined);
            expect(result.modes?.children?.valueAggregation).toBe(aggToSync);
            expect(result.modes?.combined?.valueAggregation).toBe(aggToSync);
          });

          it(`should sync both when combined=${String(aggToSync)} / children=${String(normalAgg)}`, () => {
            const config = buildValueConfig({ children: normalAgg, combined: aggToSync });
            const result = enforceAggregationConsistency(config, undefined);
            expect(result.modes?.children?.valueAggregation).toBe(aggToSync);
            expect(result.modes?.combined?.valueAggregation).toBe(aggToSync);
          });
        });
      });

      describe('Priority: respect changed side (ValueAggregation)', () => {
        it('combined changed → use combined', () => {
          const prevConfig = buildValueConfig({
            children: ValueAggregation.EmptyAggregator,
            combined: ValueAggregation.LatestAggregator,
          });
          const nextConfig = buildValueConfig({
            children: ValueAggregation.EmptyAggregator,
            combined: ValueAggregation.TextAISummaryAggregator,
          });

          const result = enforceAggregationConsistency(nextConfig, prevConfig);
          expect(result.modes?.children?.valueAggregation).toBe(ValueAggregation.TextAISummaryAggregator);
          expect(result.modes?.combined?.valueAggregation).toBe(ValueAggregation.TextAISummaryAggregator);
        });

        it('children changed → use children (concat)', () => {
          const prevConfig = buildValueConfig({
            children: ValueAggregation.LatestAggregator,
            combined: ValueAggregation.LatestAggregator,
          });
          const nextConfig = buildValueConfig({
            children: ValueAggregation.ValueConcatenateAggregator,
            combined: ValueAggregation.LatestAggregator,
          });

          const result = enforceAggregationConsistency(nextConfig, prevConfig);
          expect(result.modes?.children?.valueAggregation).toBe(ValueAggregation.ValueConcatenateAggregator);
          expect(result.modes?.combined?.valueAggregation).toBe(ValueAggregation.ValueConcatenateAggregator);
        });

        it('combined switches concat → summary', () => {
          const prevConfig = buildValueConfig({
            children: ValueAggregation.ValueConcatenateAggregator,
            combined: ValueAggregation.ValueConcatenateAggregator,
          });
          const nextConfig = buildValueConfig({
            children: ValueAggregation.ValueConcatenateAggregator,
            combined: ValueAggregation.TextAISummaryAggregator,
          });

          const result = enforceAggregationConsistency(nextConfig, prevConfig);
          expect(result.modes?.children?.valueAggregation).toBe(ValueAggregation.TextAISummaryAggregator);
          expect(result.modes?.combined?.valueAggregation).toBe(ValueAggregation.TextAISummaryAggregator);
        });

        it('children switches summary → concat', () => {
          const prevConfig = buildValueConfig({
            children: ValueAggregation.TextAISummaryAggregator,
            combined: ValueAggregation.TextAISummaryAggregator,
          });
          const nextConfig = buildValueConfig({
            children: ValueAggregation.ValueConcatenateAggregator,
            combined: ValueAggregation.TextAISummaryAggregator,
          });

          const result = enforceAggregationConsistency(nextConfig, prevConfig);
          expect(result.modes?.children?.valueAggregation).toBe(ValueAggregation.ValueConcatenateAggregator);
          expect(result.modes?.combined?.valueAggregation).toBe(ValueAggregation.ValueConcatenateAggregator);
        });
      });
    });

    describe('RULE 1: ColumnValueAggregation - Entering AI/concat mode → sync both', () => {
      const aggsToSync = [
        ColumnValueAggregation.ColumnAISummaryAggregator,
        ColumnValueAggregation.ColumnConcatenateAggregator,
      ];

      const otherAggs = [ColumnValueAggregation.ColumnLatestAggregator, ColumnValueAggregation.ColumnEmptyAggregator];

      aggsToSync.forEach((aggToSync) => {
        otherAggs.forEach((normalAgg) => {
          it(`should sync both when children=${String(aggToSync)} / combined=${String(normalAgg)}`, () => {
            const config = buildValueConfig({ children: aggToSync, combined: normalAgg });
            const result = enforceAggregationConsistency(config, undefined);
            expect(result.modes?.children?.valueAggregation).toBe(aggToSync);
            expect(result.modes?.combined?.valueAggregation).toBe(aggToSync);
          });

          it(`should sync both when combined=${String(aggToSync)} / children=${String(normalAgg)}`, () => {
            const config = buildValueConfig({ children: normalAgg, combined: aggToSync });
            const result = enforceAggregationConsistency(config, undefined);
            expect(result.modes?.children?.valueAggregation).toBe(aggToSync);
            expect(result.modes?.combined?.valueAggregation).toBe(aggToSync);
          });
        });
      });

      describe('Priority: respect changed side (ColumnValueAggregation)', () => {
        it('combined changed → use combined', () => {
          const prevConfig = buildValueConfig({
            children: ColumnValueAggregation.ColumnLatestAggregator,
            combined: ColumnValueAggregation.ColumnLatestAggregator,
          });
          const nextConfig = buildValueConfig({
            children: ColumnValueAggregation.ColumnLatestAggregator,
            combined: ColumnValueAggregation.ColumnAISummaryAggregator,
          });

          const result = enforceAggregationConsistency(nextConfig, prevConfig);
          expect(result.modes?.children?.valueAggregation).toBe(ColumnValueAggregation.ColumnAISummaryAggregator);
          expect(result.modes?.combined?.valueAggregation).toBe(ColumnValueAggregation.ColumnAISummaryAggregator);
        });
      });
    });

    describe('RULE 2: Leaving AI/concat mode → reset other to default', () => {
      const aggsToSync = [ValueAggregation.TextAISummaryAggregator, ValueAggregation.ValueConcatenateAggregator];
      const otherAggs = [ValueAggregation.LatestAggregator, ValueAggregation.EmptyAggregator];

      aggsToSync.forEach((aggToSync) => {
        otherAggs.forEach((normalAgg) => {
          it(`reset combined when children changes from ${String(aggToSync)} → ${String(normalAgg)}`, () => {
            const prevConfig = buildValueConfig({ children: aggToSync, combined: aggToSync });
            const nextConfig = buildValueConfig({ children: normalAgg, combined: aggToSync });
            const result = enforceAggregationConsistency(nextConfig, prevConfig);
            expect(result.modes?.children?.valueAggregation).toBe(normalAgg);
            expect(result.modes?.combined?.valueAggregation).toBe('default');
          });

          it(`reset children when combined changes from ${String(aggToSync)} → ${String(normalAgg)}`, () => {
            const prevConfig = buildValueConfig({ children: aggToSync, combined: aggToSync });
            const nextConfig = buildValueConfig({ children: aggToSync, combined: normalAgg });
            const result = enforceAggregationConsistency(nextConfig, prevConfig);
            expect(result.modes?.children?.valueAggregation).toBe('default');
            expect(result.modes?.combined?.valueAggregation).toBe(normalAgg);
          });
        });
      });

      // Column versions
      const colAggsToSync = [
        ColumnValueAggregation.ColumnAISummaryAggregator,
        ColumnValueAggregation.ColumnConcatenateAggregator,
      ];
      const otherColAggs = [ColumnValueAggregation.ColumnLatestAggregator];

      colAggsToSync.forEach((aggToSync) => {
        otherColAggs.forEach((normalAgg) => {
          it(`(column) reset combined when children changes from ${String(aggToSync)} → ${String(normalAgg)}`, () => {
            const prevConfig = buildValueConfig({ children: aggToSync, combined: aggToSync });
            const nextConfig = buildValueConfig({ children: normalAgg, combined: aggToSync });
            const result = enforceAggregationConsistency(nextConfig, prevConfig);
            expect(result.modes?.children?.valueAggregation).toBe(normalAgg);
            expect(result.modes?.combined?.valueAggregation).toBe('default');
          });

          it(`(column) reset children when combined changes from ${String(aggToSync)} → ${String(normalAgg)}`, () => {
            const prevConfig = buildValueConfig({ children: aggToSync, combined: aggToSync });
            const nextConfig = buildValueConfig({ children: aggToSync, combined: normalAgg });
            const result = enforceAggregationConsistency(nextConfig, prevConfig);
            expect(result.modes?.children?.valueAggregation).toBe('default');
            expect(result.modes?.combined?.valueAggregation).toBe(normalAgg);
          });
        });
      });
    });

    describe('RULE 3: Both non-AI/concat → keep unchanged', () => {
      it('unchanged when both are default', () => {
        const config = buildValueConfig({ children: 'default', combined: 'default' });
        expect(enforceAggregationConsistency(config, undefined)).toEqual(config);
      });

      it('unchanged when switching between non-AI/concat aggregators', () => {
        const prevConfig = buildValueConfig({
          children: ValueAggregation.LatestAggregator,
          combined: ValueAggregation.ValueCountAggregator,
        });
        const nextConfig = buildValueConfig({
          children: ValueAggregation.EmptyAggregator,
          combined: ValueAggregation.ValueCountAggregator,
        });
        expect(enforceAggregationConsistency(nextConfig, prevConfig)).toEqual(nextConfig);
      });

      it('mix default + non-AI/concat stays unchanged', () => {
        const config = buildValueConfig({ children: 'default', combined: ValueAggregation.LatestAggregator });
        expect(enforceAggregationConsistency(config, undefined)).toEqual(config);
      });
    });

    // Edge cases
    describe('Edge cases', () => {
      it('handles missing modes', () => {
        const config = { modes: {} } as BaseAggregationConfig<ValueAggregationOverride>;
        expect(enforceAggregationConsistency(config, undefined)).toEqual(config);
      });

      it('preserves weightFormula and other fields', () => {
        const config: BaseAggregationConfig<ValueAggregationOverride> = {
          modes: {
            children: {
              valueAggregation: ValueAggregation.TextAISummaryAggregator,
              weightFormula: 'foo',
            },
            combined: {
              valueAggregation: ValueAggregation.LatestAggregator,
              weightFormula: 'bar',
            },
          },
        };

        const result = enforceAggregationConsistency(config, undefined);
        expect(result.modes?.children?.weightFormula).toBe('foo');
        expect(result.modes?.combined?.weightFormula).toBe('bar');
        expect(result.modes?.children?.valueAggregation).toBe(ValueAggregation.TextAISummaryAggregator);
        expect(result.modes?.combined?.valueAggregation).toBe(ValueAggregation.TextAISummaryAggregator);
      });
    });
  });
});
