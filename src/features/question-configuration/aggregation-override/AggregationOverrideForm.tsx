import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Modal<PERSON>ooter, Label } from 'reactstrap';
import { Loader } from '@g17eco/atoms/loader';
import { BulkActionUtr } from '@g17eco/types/survey-question-list';
import { AggregationMode, UtrValueType } from '@g17eco/core';
import { AggregationConfigDropdown } from './AggregationConfigDropdown';
import { TableColumnConfiguration } from './TableColumnConfiguration';
import { enforceAggregationConsistency, getAggregationOptions, getStorageKey } from './utils';
import {
  AggregationOverrideData,
  AggregationConfigOverride,
  TableOverride,
  InitiativeUniversalTracker,
} from '@g17eco/types/initiativeUniversalTracker';

import { useAISummaryEnforcement } from './hooks/useAISummaryEnforcement';
import { SerializedError } from '@reduxjs/toolkit';
import { QueryError } from '@g17eco/molecules/query/QueryError';
import { WarningModal } from './WarningModal';
import { SettingStorage } from '@services/SettingStorage';

interface Props {
  isLoading: boolean;
  error?:
    | {
        message: string;
        name: string;
      }
    | SerializedError;
  initialData: AggregationOverrideData;
  selectedQuestion: BulkActionUtr;
  handleUpdateAggregationOverride: (aggregationOverride: AggregationOverrideData) => void;
  handleCloseModal: () => void;
  initiativeUtrMap: Map<string, InitiativeUniversalTracker>;
  rootInitiativeId: string | undefined;
}

export const AggregationOverrideForm = (props: Props) => {
  const {
    isLoading,
    error,
    initialData,
    selectedQuestion,
    handleUpdateAggregationOverride,
    handleCloseModal,
    initiativeUtrMap,
    rootInitiativeId,
  } = props;
  const [overrideData, setOverrideData] = useState<AggregationOverrideData>(initialData);

  const [showWarning, setShowWarning] = useState(false);

  const initiativeUtr = initiativeUtrMap.get(selectedQuestion._id);
  const { getAISummaryInfo } = useAISummaryEnforcement(initiativeUtr);

  const hasAnythingChanged = JSON.stringify(initialData) !== JSON.stringify(overrideData);

  const shouldShowWarning = () => {
    if (!rootInitiativeId) {
      return false;
    }
    const key = getStorageKey(rootInitiativeId);
    const dismissed = SettingStorage.getItem(key);
    return !dismissed;
  };

  const onClickUpdate = () => {
    if (!hasAnythingChanged) {
      return;
    }
    if (shouldShowWarning()) {
      setShowWarning(true);
      return;
    }
    handleUpdateAggregationOverride(overrideData);
  };

  const onChangeAggregationConfig = (aggregationConfig: AggregationConfigOverride) => {
    // Enforce AI summary consistency for text value types
    if (valueType === UtrValueType.Text) {
      aggregationConfig = enforceAggregationConsistency(aggregationConfig, overrideData.aggregationConfig);
    }

    setOverrideData((prev) => ({ ...prev, aggregationConfig }));
  };

  const onChangeTableOverride = (table: TableOverride) => {
    setOverrideData((prev) => ({ ...prev, table }));
  };

  const valueType = selectedQuestion.valueType;
  const summaryInfo = getAISummaryInfo();
  const [childrenOptions, combinedOptions] = [AggregationMode.Children, AggregationMode.Combined].map((mode) =>
    getAggregationOptions({
      valueType,
      mode,
      aiSummary: summaryInfo,
    }),
  );

  return (
    <>
      <ModalBody>
        {isLoading ? <Loader /> : null}
        {error ? <QueryError error={error} /> : null}
        <div className='mb-3'>
          <Label className='strong mb-3'>Aggregation</Label>
          <AggregationConfigDropdown
            valueType={valueType as UtrValueType}
            childrenOptions={childrenOptions}
            combinedOptions={combinedOptions}
            aggregationConfig={overrideData.aggregationConfig}
            onChange={onChangeAggregationConfig}
          />
        </div>

        {valueType === UtrValueType.Table && (
          <div className='mt-4'>
            <Label style={{ fontWeight: 500 }}>Table Column Configuration</Label>
            <TableColumnConfiguration
              selectedQuestion={selectedQuestion}
              table={overrideData.table}
              onChangeTableOverride={onChangeTableOverride}
              initiativeUtr={initiativeUtr}
            />
          </div>
        )}
      </ModalBody>
      <ModalFooter>
        <Button color='transparent' onClick={handleCloseModal}>
          Cancel
        </Button>
        <Button color='primary' disabled={isLoading || !hasAnythingChanged} onClick={onClickUpdate}>
          Save
        </Button>
      </ModalFooter>
      <WarningModal
        isOpen={showWarning}
        onConfirm={(dontRemind: boolean) => {
          if (rootInitiativeId && dontRemind) {
            const key = getStorageKey(rootInitiativeId);
            SettingStorage.setItem(key, 'true');
          }
          handleUpdateAggregationOverride(overrideData);
          setShowWarning(false);
        }}
        onCancel={() => {
          setShowWarning(false);
        }}
      />
    </>
  );
};
