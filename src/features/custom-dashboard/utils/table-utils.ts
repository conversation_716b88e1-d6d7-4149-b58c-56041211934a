import { GridDashboardTableItem } from '@g17eco/types/insight-custom-dashboard';
import { TableData, TableRowDataInfo } from '../types';
import { UtrVariable, UtrVariables } from '@routes/summary/insights/utils/constants';
import { extractVariables } from '@utils/formula';
import { generateId } from '@utils/index';
import { UniversalTrackerBlueprintMin, UtrValueType } from '@g17eco/types/universalTracker';

export const MAX_LENGTH_TABLE_TITLE = 200;

export const updateRow = (data: TableRowDataInfo[], row: Partial<TableRowDataInfo>) => {
  let found = false;
  const newData = data.map((r) => {
    if (r.id === row.id) {
      found = true;
      return { ...r, ...row };
    }
    return r;
  });
  return found ? newData : data;
};

export const removeRow = (data: TableRowDataInfo[], id: string) => {
  return data.filter((r) => r.id !== id);
};

export const getTableData = (item: GridDashboardTableItem | Pick<GridDashboardTableItem, 'type'>): TableData => {
  const tableData: TableData = {
    rowData: [],
    editRowId: '',
  };

  if (!('variables' in item && 'calculation' in item) || !item.variables || !item.calculation?.values) {
    return tableData;
  }

  return item.calculation.values.reduce((acc, value) => {
    // get formula and extract keys (a, b, c, ...)
    // create utrVariable from extracted keys
    const rowData: TableRowDataInfo = {
      id: generateId(),
      data: {
        value,
        variables: [],
      },
    };
    if (value.formula) {
      rowData.data.variables = extractVariables(value.formula).reduce((acc, variableLetter) => {
        if (item.variables?.[variableLetter]) {
          acc.push(item.variables[variableLetter]);
        }
        return acc;
      }, [] as UtrVariable[]);
    }

    acc.rowData.push(rowData);
    return acc;
  }, tableData);
};

export const hasValidVariables = (rowData: TableRowDataInfo[]) => {
  for (const row of rowData) {
    for (const variable of row.data.variables) {
      if (!variable.code) {
        return false;
      }
    }
  }
  return true;
};

const isDirectTable = (row: TableRowDataInfo, questionMap: Map<string, UniversalTrackerBlueprintMin>) => {
  const metric = row.data.variables[0];
  const question = questionMap.get(metric?.code);
  if (!question) {
    return false;
  }
  
  // A direct table does not have a value list code set up
  return question.valueType === UtrValueType.Table && !metric?.valueListCode;
};

export const getDirectTableRowData = (
  rowData: TableRowDataInfo[],
  blueprintQuestions: UniversalTrackerBlueprintMin[],
) => {
  const questionMap = blueprintQuestions.reduce((acc, question) => {
    acc.set(question.code, question);
    return acc;
  }, new Map<string, UniversalTrackerBlueprintMin>());
  return rowData.filter((row) => isDirectTable(row, questionMap));
};

export const isValidTableData = (rowData: TableRowDataInfo[], blueprintQuestions: UniversalTrackerBlueprintMin[] = []) => {
  if (rowData.length === 0 || !hasValidVariables(rowData)) {
    return false;
  }
  // If there is only one row, don't need to check for direct table
  if (rowData.length <= 1) {
    return true;
  }
  const directTables = getDirectTableRowData(rowData, blueprintQuestions);
  return directTables.length < 1;
};

export const getUniqueUtrVariables = (rowData: TableRowDataInfo[]) => {
  return rowData.reduce((acc, row) => {
    const newVariables = row.data.variables.filter(({ code, valueListCode = '' }) => {
      if (acc.length === 0) {
        return true;
      }
      return acc.every(
        ({ code: existingCode, valueListCode: existingValueListCode = '' }) =>
          existingCode !== code || existingValueListCode !== valueListCode,
      );
    });
    return acc.concat(newVariables);
  }, [] as UtrVariable[]);
};

export const getCalculationValues = ({
  rowData,
  variables,
}: {
  rowData: TableRowDataInfo[];
  variables: UtrVariables;
}) => {
  return rowData.map((row) => {
    const key = Object.keys(variables).find(
      (key) =>
        variables[key].valueListCode === row.data.variables[0].valueListCode &&
        variables[key].code === row.data.variables[0].code,
    );
    return {
      name: row.data.value.name,
      formula: `{${key}}`,
    };
  });
};
