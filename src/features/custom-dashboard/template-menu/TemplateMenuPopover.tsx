import { useRef } from 'react';
import { Popover, PopoverBody, PopoverProps, UncontrolledPopover } from 'reactstrap';
import { useClickOutside } from '@hooks/useClickOutside';
import { DashboardTemplateType, TemplateOption } from '@g17eco/types/insight-custom-dashboard';
import './style.scss';

const BLANK_OPTION: TemplateOption = {
  label: 'Blank template',
  value: DashboardTemplateType.Blank,
  icon: 'fal fa-square-plus',
};
const getOptions = (templates: TemplateOption[]): TemplateOption[] => {
  return [BLANK_OPTION, ...templates];
};

interface Props {
  templates: TemplateOption[];
  open?: boolean;
  toggle?: () => void;
  isUncontrolled?: boolean;
  target: string;
  onClickOption: (value: DashboardTemplateType) => void;
  onClickOutside?: () => void;
}

export const TemplateMenuPopover = ({
  templates,
  open,
  toggle,
  isUncontrolled,
  target,
  onClickOption,
  onClickOutside,
}: Props) => {
  const popoverRef = useRef<HTMLDivElement>(null);
  useClickOutside(popoverRef, onClickOutside);

  if (!templates.length) {
    return null;
  }

  const options = getOptions(templates);

  const body = (
    <PopoverBody>
      <div ref={popoverRef}>
        {options.map(({ value, label, icon }) => {
          const isBlankTemplate = value === DashboardTemplateType.Blank;

          return (
            <div
              key={value}
              onClick={() => onClickOption(value as DashboardTemplateType)}
              className={'insights-dashboard__template-menu-popover__item d-flex align-items-center text-md px-3 py-1'}
            >
              {icon && isBlankTemplate ? <i className={`${icon} mr-2 text-ThemeIconSecondary`} /> : null}
              {icon && !isBlankTemplate ? (
                <img src={icon} className='mr-2 object-fit-cover ' style={{ width: 16, height: 16 }} />
              ) : null}
              {label}
            </div>
          );
        })}
      </div>
    </PopoverBody>
  );

  const commonProps = {
    placement: 'bottom-start',
    container: 'body',
    hideArrow: true,
    trigger: isUncontrolled ? 'focus' : undefined,
    target,
    className: 'insights-dashboard__template-menu-popover',
  } satisfies PopoverProps;

  if (isUncontrolled) {
    return <UncontrolledPopover {...commonProps}>{body}</UncontrolledPopover>;
  }

  return open ? (
    <Popover {...commonProps} isOpen={open} toggle={toggle}>
      {body}
    </Popover>
  ) : null;
};
