import { HistoricalUtrs } from '@g17eco/types/insight-custom-dashboard';
import { getActualUtrsData } from './utils';
import { sortEffectiveDateAsc } from '@utils/sort';
import './TableWidget.scss';
import { ColumnDef } from '@tanstack/react-table';
import { Table } from '@g17eco/molecules/table';
import { InputColumn } from '@g17eco/types/questionInterfaces';
import { getTableConfiguration } from '@utils/universalTracker';
import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';
import { UniversalTrackerModalServiceUtrv } from '@g17eco/types/universalTrackerValue';
import { truncate } from '@utils/string';
import { getUnitNumberScaleColumn } from '@features/report-output/questionView';
import { useContext } from 'react';
import { CustomDashboardContext } from '@routes/custom-dashboard/context/CustomDashboardWrapper';

const LIMIT_TRUNCATE_LETTERS = 40;

export type DirectTableData = {
  utrv: UniversalTrackerModalServiceUtrv;
  data: {
    [columnCode: string]: string | number | string[] | undefined;
  };
};

const getTableData = ({
  utrsData,
  redirectUtrvSource,
}: {
  utrsData: HistoricalUtrs[];
  redirectUtrvSource: (utrv: UniversalTrackerModalServiceUtrv) => void;
}) => {
  const actualUtrsData = getActualUtrsData(utrsData);

  // Get the first UTR data for direct table
  const utrData = actualUtrsData[0];

  if (!utrData) {
    return { columns: [], data: [] };
  }

  const tableConfig = getTableConfiguration(utrData.utr);

  if (!tableConfig || !tableConfig.columns) {
    return { columns: [], data: [] };
  }

  const sortedUtrvs = [...utrData.utrvs].sort(sortEffectiveDateAsc);

  const data: DirectTableData[] = sortedUtrvs
    .map((utrv) => {
      const tableRows: InputColumn[][] = utrv.valueData?.table ?? [];

      return tableRows.map((row: InputColumn[]) => {
        const rowData: DirectTableData = {
          utrv: utrv,
          data: {},
        };

        row.forEach((cell) => {
          const columnDefinition = tableConfig.columns.find((col) => col.code === cell.code);
          if (!columnDefinition) {
            rowData.data[cell.code] = cell.value;
            return;
          }

          const unitColumn = getUnitNumberScaleColumn(columnDefinition, undefined, cell, true);
          rowData.data[cell.code] = cell.value ? `${cell.value}${unitColumn}` : '';
        });

        return rowData;
      });
    })
    .flat();

  const columns: ColumnDef<DirectTableData>[] = tableConfig.columns.map((column) => ({
    id: column.code,
    header: () => (
      <SimpleTooltip text={column.shortName || column.name}>{column.shortName || column.name}</SimpleTooltip>
    ),
    meta: {
      headerProps: {
        className: 'text-truncate',
        style: {
          maxWidth: 250,
        },
      },
      cellProps: {
        className: 'text-center',
        style: { cursor: 'pointer' },
      },
    },
    cell: ({ row }) => {
      const value = row.original.data[column.code];
      if (value === undefined || value === null) {
        return '';
      }

      const handleCellClick = () => {
        if (row.original.utrv && redirectUtrvSource) {
          redirectUtrvSource(row.original.utrv);
        }
      };

      const displayValue = Array.isArray(value) ? value.join(', ') : String(value);
      const isTruncated = displayValue.length > LIMIT_TRUNCATE_LETTERS;
      const truncatedValue = truncate(displayValue, LIMIT_TRUNCATE_LETTERS);

      return (
        <SimpleTooltip text={isTruncated ? displayValue : undefined}>
          <span className='d-inline-block' onClick={handleCellClick}>
            {truncatedValue}
          </span>
        </SimpleTooltip>
      );
    },
    enableSorting: false,
  }));

  return {
    columns,
    data,
  };
};

export interface DirectTableWidgetProps {
  utrsData: HistoricalUtrs[];
}

export const DirectTableWidget = (props: DirectTableWidgetProps) => {
  const { utrsData } = props;
  const { redirectUtrvSource = () => {} } = useContext(CustomDashboardContext);

  if (!utrsData.length) {
    return null;
  }

  const { columns, data } = getTableData({
    utrsData,
    redirectUtrvSource,
  });

  return (
    <div className='w-100 h-100 table-widget__container direct-table-widget__container'>
      <Table columns={columns} data={data} />
      {data.length === 0 ? <p className='py-2 strong text-center text-muted bg-white'>No data available</p> : null}
    </div>
  );
};
