import Dashboard, { DashboardRow } from '@g17eco/molecules/dashboard';
import { ROUTES } from '@constants/routes';
import { Breadcrumbs } from '@g17eco/molecules/breadcrumbs';
import { generateUrl } from '@routes/util';
import { useHistory, useParams } from 'react-router-dom';
import { PPTXReportsContainer } from './PPTXReportsContainer';
import { useEffect } from 'react';
import { useAppSelector, type RootState } from '@reducers/index';
import { selectMostRecentSurveyByPeriodAndType, selectWithDefault } from '@g17eco/slices/initiativeSurveyListSlice';
import { SurveyType } from '@g17eco/types/surveyCommon';

export const PPTXReportsRoute = () => {
  const { initiativeId, surveyId } = useParams<{ initiativeId: string; surveyId?: string }>();
  const history = useHistory();
  const rootUrl = generateUrl(ROUTES.DOWNLOADS, { initiativeId });
  const surveys = useAppSelector(selectWithDefault);
  const mostRecentSurvey = useAppSelector((state: RootState) =>
    selectMostRecentSurveyByPeriodAndType(state, undefined, SurveyType.Default),
  );

  const selectedSurvey = surveys.find((s) => s._id === surveyId);

  const handleSelectSurvey = (surveyId: string) => {
    history.push(generateUrl(ROUTES.DOWNLOADS_PPTX, { initiativeId, surveyId }));
  };

  useEffect(() => {
    if (!selectedSurvey && mostRecentSurvey) {
      handleSelectSurvey(mostRecentSurvey._id);
    }
  }, [selectedSurvey, mostRecentSurvey]);

  const breadcrumbs = [{ label: 'AI Enhanced Sustainability Reports' }];

  return (
    <Dashboard>
      <DashboardRow>
        <Breadcrumbs breadcrumbs={breadcrumbs} rootLabel='Downloads' rootUrl={rootUrl} />
      </DashboardRow>
      <PPTXReportsContainer
        initiativeId={initiativeId}
        surveyId={selectedSurvey?._id}
        handleSelectSurvey={handleSelectSurvey}
      />
    </Dashboard>
  );
};
